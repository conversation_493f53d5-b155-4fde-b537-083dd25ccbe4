<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.maintenance.risk.infrastructure.repository.risk.mapper.RiskMapper">
    <sql id="Base_Column_List">
        id,type,workflow_id,code,name,status,handler,creator,gmt_create,describe,attachment,
        flexible_str1,flexible_str2,flexible_str3,flexible_str4,flexible_str5,
        flexible_str6,flexible_str7,flexible_str8,flexible_str9,flexible_str10,
        flexible_str11,flexible_str12,flexible_str13,flexible_str14, flexible_str15,
        flexible_str16,flexible_str17,flexible_str18,flexible_str19,flexible_str20,
        flexible_int1,flexible_int2,flexible_int3,flexible_int4,flexible_int5,
        flexible_int6,flexible_int7,flexible_int8,flexible_int9,flexible_int10
    </sql>

    <select id="findById" resultType="com.zte.uedm.maintenance.malfunction.infrastructure.repository.malfunction.po.WorkflowInstance">
        select <include refid="Base_Column_List"/> from workflow_instance_risk
        where id = #{id}
    </select>

    <select id="findByWorkflowId" resultType="com.zte.uedm.maintenance.malfunction.infrastructure.repository.malfunction.po.WorkflowInstance">
        select <include refid="Base_Column_List"/> from workflow_instance_risk
        where workflow_id = #{workflowId}
    </select>

    <select id="findByRelateOrderId" resultType="java.lang.Integer">
        select count(*) from workflow_instance_risk
        where flexible_str7 = #{relateOrderId}
        <if test="id != null and id !=''">
            and id !=#{id}
        </if>
    </select>

    <delete id="deleteById">
        delete from workflow_instance_risk
        where id = #{id}
    </delete>

    <select id="queryListByCondition" resultType="com.zte.uedm.maintenance.malfunction.infrastructure.repository.malfunction.po.MalfunctionPO">
        select id, workflow_id, code as malfunctionCode, name as malfunctionName, flexible_str4 as reporter, handler, flexible_str1 as malfunctionClassify,
        flexible_str5 as malfunctionHappenTime, status, flexible_int4 as malfunctionSource, flexible_int1 as effectLevel, flexible_int2 as emergencyLevel,
        flexible_int3 as malfunctionLevel, flexible_str2 as position, flexible_str3 as relateOrder,flexible_str6 as positionId,flexible_str7 as relateOrderId, describe, attachment, creator, gmt_create
        from workflow_instance_risk
        where type = 'MALFUNCTION'
        <if test="query.handlerName != null and query.handlerName != ''">
            and handler = #{query.handlerName}
        </if>
        <if test="query.creatorName != null and query.creatorName != ''">
            and creator = #{query.creatorName}
        </if>
        <if test="query.workflowIds != null and query.workflowIds.size()>0">
            and workflow_id in
            <foreach collection="query.workflowIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <!--查看所有工单tab页筛选-->
        <if test="query.isSearchAll">
            <choose>
                <when test="!query.allPermission">
                    and workflow_id is not null
                    and (
                    flexible_str4 = #{query.user} or creator =#{query.user} or (handler = #{query.user} and status != 0)
                    <if test="query.selfDownWorkflowIds != null and query.selfDownWorkflowIds.size > 0">
                        or workflow_id in
                        <foreach collection="query.selfDownWorkflowIds" item="downId" separator="," open="(" close=")">
                            #{downId}
                        </foreach>
                    </if>
                    )
                </when>
                <otherwise>
                    and (workflow_id is not null or (workflow_id is null and creator =#{query.user}))
                </otherwise>
            </choose>
        </if>
        <if test="query.malfunctionName != null and query.malfunctionName != ''">
            and upper(name) like concat('%', upper(#{query.malfunctionName}), '%')
        </if>
        <if test="query.malfunctionLevel != null and query.malfunctionLevel.size()>0">
            and flexible_int3 in
            <foreach collection="query.malfunctionLevel" item="level" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>
        <if test="query.status != null and query.status.size()>0">
            and status in
            <foreach collection="query.status" item="state" open="(" separator="," close=")">
                #{state}
            </foreach>
        </if>
        <if test="query.malfunctionCode != null and query.malfunctionCode != ''">
            and upper(code) like concat('%', upper(#{query.malfunctionCode}), '%')
        </if>
        <if test="query.reporter != null and query.reporter != ''">
            and upper(flexible_str4) like concat('%', upper(#{query.reporter}), '%')
        </if>
        <if test="query.handler != null and query.handler != ''">
            and case when status = 0 then upper(creator) else upper(handler) end like concat('%', upper(#{query.handler}), '%')
        </if>
        <if test="query.creator != null and query.creator != ''">
            and upper(creator) like concat('%', upper(#{query.creator}), '%')
        </if>
        <if test="query.happenStartTime != null and query.happenStartTime != ''">
            and flexible_str5 &gt;= #{query.happenStartTime}
        </if>
        <if test="query.happenEndTime != null and query.happenEndTime != ''">
            and flexible_str5 &lt;= #{query.happenEndTime}
        </if>
        <if test="query.emergencyLevel != null and query.emergencyLevel.size()>0">
            and flexible_int2 in
            <foreach collection="query.emergencyLevel" item="emergency" open="(" separator="," close=")">
                #{emergency}
            </foreach>
        </if>
        <if test="query.malfunctionSource != null and query.malfunctionSource.size()>0">
            and flexible_int4 in
            <foreach collection="query.malfunctionSource" item="source" open="(" separator="," close=")">
                #{source}
            </foreach>
        </if>
        <if test="query.effectLevel != null and query.effectLevel.size()>0">
            and flexible_int1 in
            <foreach collection="query.effectLevel" item="effect" open="(" separator="," close=")">
                #{effect}
            </foreach>
        </if>
        <if test="query.sort != null and query.sort != ''">
            order by ${query.sort} ${query.action}
        </if>
        <if test="query.sort ==null or query.sort ==''">
            order by status asc, gmt_create desc
        </if>
    </select>


    <select id="queryMalfunctionPOListByCondition" resultType="com.zte.uedm.maintenance.malfunction.infrastructure.repository.malfunction.po.MalfunctionPO">
        select id, workflow_id, code as malfunctionCode, name as malfunctionName, flexible_str4 as reporter, handler, flexible_str1 as malfunctionClassify,
        flexible_str5 as malfunctionHappenTime, status, flexible_int4 as malfunctionSource, flexible_int1 as effectLevel, flexible_int2 as emergencyLevel,
        flexible_int3 as malfunctionLevel, flexible_str2 as position, flexible_str3 as relateOrder,flexible_str6 as positionId,flexible_str7 as relateOrderId, describe, attachment, creator, gmt_create
        from workflow_instance_risk
        where type = 'MALFUNCTION'
        <if test="query.handlerName != null and query.handlerName != ''">
            and handler = #{query.handlerName}
        </if>
        <if test="query.creatorName != null and query.creatorName != ''">
            and creator = #{query.creatorName}
        </if>
        <if test="query.workflowIds != null and query.workflowIds.size()>0">
            and workflow_id in
            <foreach collection="query.workflowIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="!query.allPermission">
            and (flexible_str4 = #{query.user} or handler = #{query.user} or creator =#{query.user})
        </if>
        <if test="query.malfunctionName != null and query.malfunctionName != ''">
            and upper(name) like concat('%', upper(#{query.malfunctionName}), '%')
        </if>
        <if test="query.malfunctionLevel != null and query.malfunctionLevel.size()>0">
            and flexible_int3 in
            <foreach collection="query.malfunctionLevel" item="level" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>
        <if test="query.status != null and query.status.size()>0">
            and status in
            <foreach collection="query.status" item="state" open="(" separator="," close=")">
                #{state}
            </foreach>
        </if>
        <if test="query.malfunctionCode != null and query.malfunctionCode != ''">
            and upper(code) like concat('%', upper(#{query.malfunctionCode}), '%')
        </if>
        <if test="query.reporter != null and query.reporter != ''">
            and upper(flexible_str4) like concat('%', upper(#{query.reporter}), '%')
        </if>
        <if test="query.handler != null and query.handler != ''">
            and case when status = 0 then upper(creator) else upper(handler) end like concat('%', upper(#{query.handler}), '%')
        </if>
        <if test="query.creator != null and query.creator != ''">
            and upper(creator) like concat('%', upper(#{query.creator}), '%')
        </if>
        <if test="query.happenStartTime != null and query.happenStartTime != ''">
            and flexible_str5 &gt;= #{query.happenStartTime}
        </if>
        <if test="query.happenEndTime != null and query.happenEndTime != ''">
            and flexible_str5 &lt;= #{query.happenEndTime}
        </if>
        <if test="query.emergencyLevel != null and query.emergencyLevel.size()>0">
            and flexible_int2 in
            <foreach collection="query.emergencyLevel" item="emergency" open="(" separator="," close=")">
                #{emergency}
            </foreach>
        </if>
        <if test="query.malfunctionSource != null and query.malfunctionSource.size()>0">
            and flexible_int4 in
            <foreach collection="query.malfunctionSource" item="source" open="(" separator="," close=")">
                #{source}
            </foreach>
        </if>
        <if test="query.effectLevel != null and query.effectLevel.size()>0">
            and flexible_int1 in
            <foreach collection="query.effectLevel" item="effect" open="(" separator="," close=")">
                #{effect}
            </foreach>
        </if>
        <if test="query.sort != null and query.sort != ''">
            order by ${query.sort} ${query.action}
        </if>
        <if test="query.sort ==null or query.sort ==''">
            order by status asc, gmt_create desc
        </if>
    </select>


    <select id="queryListByCond" resultType="com.zte.uedm.maintenance.malfunction.infrastructure.repository.malfunction.po.MalfunctionPO">
        select id, workflow_id, code as malfunctionCode, name as malfunctionName, flexible_str4 as reporter, handler, flexible_str1 as malfunctionClassify,
        flexible_str5 as malfunctionHappenTime, status, flexible_int4 as malfunctionSource, flexible_int1 as effectLevel, flexible_int2 as emergencyLevel,
        flexible_int3 as malfunctionLevel, flexible_str2 as position, flexible_str3 as relateOrder,flexible_str6 as positionId,flexible_str7 as relateOrderId, describe, attachment, creator, gmt_create
        from workflow_instance_risk
        where type = 'MALFUNCTION'
        <if test="query.handlerName != null and query.handlerName != ''">
            and handler = #{query.handlerName}
        </if>
        <if test="query.creatorName != null and query.creatorName != ''">
            and creator = #{query.creatorName}
        </if>
        <if test="workflowIds != null and workflowIds.size()>0">
            and workflow_id in
            <foreach collection="workflowIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="!query.allPermission">
            and (flexible_str4 = #{query.user} or handler = #{query.user} or creator =#{query.user})
        </if>
        <if test="query.malfunctionName != null and query.malfunctionName != ''">
            and upper(name) like concat('%', upper(#{query.malfunctionName}), '%')
        </if>
        <if test="query.malfunctionLevel != null and query.malfunctionLevel.size()>0">
            and flexible_int3 in
            <foreach collection="query.malfunctionLevel" item="level" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>
        <if test="query.effectLevel != null and query.effectLevel.size()>0">
            and flexible_int1 in
            <foreach collection="query.effectLevel" item="effect" open="(" separator="," close=")">
                #{effect}
            </foreach>
        </if>
        <if test="query.status != null and query.status.size()>0">
            and status in
            <foreach collection="query.status" item="state" open="(" separator="," close=")">
                #{state}
            </foreach>
        </if>
        <if test="query.malfunctionCode != null and query.malfunctionCode != ''">
            and upper(code) like concat('%', upper(#{query.malfunctionCode}), '%')
        </if>
        <if test="query.reporter != null and query.reporter != ''">
            and upper(flexible_str4) like concat('%', upper(#{query.reporter}), '%')
        </if>
        <if test="query.handler != null and query.handler != ''">
            and upper(handler) like concat('%', upper(#{query.handler}), '%')
        </if>
        <if test="query.creator != null and query.creator != ''">
            and upper(creator) like concat('%', upper(#{query.creator}), '%')
        </if>
        <if test="query.happenStartTime != null and query.happenStartTime != ''">
            and flexible_str5 &gt;= #{query.happenStartTime}
        </if>
        <if test="query.happenEndTime != null and query.happenEndTime != ''">
            and flexible_str5 &lt;= #{query.happenEndTime}
        </if>
        <if test="query.emergencyLevel != null and query.emergencyLevel.size()>0">
            and flexible_int2 in
            <foreach collection="query.emergencyLevel" item="emergency" open="(" separator="," close=")">
                #{emergency}
            </foreach>
        </if>
        <if test="query.malfunctionSource != null and query.malfunctionSource.size()>0">
            and flexible_int4 in
            <foreach collection="query.malfunctionSource" item="source" open="(" separator="," close=")">
                #{source}
            </foreach>
        </if>
        <if test="query.sort ==null or query.sort ==''">
            order by status asc, gmt_create desc
        </if>
    </select>

    <select id="checkName" resultType="integer" >
        select count(*) from workflow_instance_risk where name = #{name}
        <if test="id != null and id !=''">
            and id !=#{id}
        </if>
    </select>

    <insert id="insert">
        insert into workflow_instance_risk(<include refid="Base_Column_List"/>)
        values(#{workflowInstance.id}, #{workflowInstance.type},#{workflowInstance.workflowId}, #{workflowInstance.code}, #{workflowInstance.name}, #{workflowInstance.status},
        #{workflowInstance.handler}, #{workflowInstance.creator}, #{workflowInstance.gmtCreate}, #{workflowInstance.describe}, #{workflowInstance.attachment},
        #{workflowInstance.flexibleStr1},#{workflowInstance.flexibleStr2},#{workflowInstance.flexibleStr3},#{workflowInstance.flexibleStr4},#{workflowInstance.flexibleStr5},
        #{workflowInstance.flexibleStr6},#{workflowInstance.flexibleStr7},#{workflowInstance.flexibleStr8},#{workflowInstance.flexibleStr9},#{workflowInstance.flexibleStr10},
        #{workflowInstance.flexibleStr11},#{workflowInstance.flexibleStr12},#{workflowInstance.flexibleStr13},#{workflowInstance.flexibleStr14},#{workflowInstance.flexibleStr15},
        #{workflowInstance.flexibleStr16},#{workflowInstance.flexibleStr17},#{workflowInstance.flexibleStr18},#{workflowInstance.flexibleStr19},#{workflowInstance.flexibleStr20},
        #{workflowInstance.flexibleInt1},#{workflowInstance.flexibleInt2},#{workflowInstance.flexibleInt3},#{workflowInstance.flexibleInt4},#{workflowInstance.flexibleInt5},
        #{workflowInstance.flexibleInt6},#{workflowInstance.flexibleInt7},#{workflowInstance.flexibleInt8},#{workflowInstance.flexibleInt9},#{workflowInstance.flexibleInt10}
        ) ON conflict(id) do update set id =#{workflowInstance.id},type =#{workflowInstance.type},workflow_id = #{workflowInstance.workflowId},code = #{workflowInstance.code}, name = #{workflowInstance.name}, status = #{workflowInstance.status},
        handler = #{workflowInstance.handler}, creator = #{workflowInstance.creator},gmt_create = #{workflowInstance.gmtCreate},describe = #{workflowInstance.describe},attachment = #{workflowInstance.attachment},
        flexible_str1 = #{workflowInstance.flexibleStr1},flexible_str2 = #{workflowInstance.flexibleStr2},flexible_str3 = #{workflowInstance.flexibleStr3},flexible_str4 = #{workflowInstance.flexibleStr4},flexible_str5 = #{workflowInstance.flexibleStr5},
        flexible_str6 = #{workflowInstance.flexibleStr6},flexible_str7 = #{workflowInstance.flexibleStr7},flexible_str8 = #{workflowInstance.flexibleStr8},flexible_str9 = #{workflowInstance.flexibleStr9},flexible_str10 = #{workflowInstance.flexibleStr10},
        flexible_str11 = #{workflowInstance.flexibleStr11},flexible_str12 = #{workflowInstance.flexibleStr12},flexible_str13 = #{workflowInstance.flexibleStr13},flexible_str14 = #{workflowInstance.flexibleStr14},flexible_str15 = #{workflowInstance.flexibleStr15},
        flexible_str16 = #{workflowInstance.flexibleStr16},flexible_str17 = #{workflowInstance.flexibleStr17},flexible_str18 = #{workflowInstance.flexibleStr18},flexible_str19 = #{workflowInstance.flexibleStr19},flexible_str20 = #{workflowInstance.flexibleStr20},
        flexible_int1 = #{workflowInstance.flexibleInt1},flexible_int2 = #{workflowInstance.flexibleInt2},flexible_int3 = #{workflowInstance.flexibleInt3},flexible_int4 = #{workflowInstance.flexibleInt4},flexible_int5 = #{workflowInstance.flexibleInt5},
        flexible_int6 = #{workflowInstance.flexibleInt6},flexible_int7 = #{workflowInstance.flexibleInt7},flexible_int8 = #{workflowInstance.flexibleInt8},flexible_int9 = #{workflowInstance.flexibleInt9},flexible_int10 = #{workflowInstance.flexibleInt10}
    </insert>

    <select id="getMalfunctionClassify" resultType="com.zte.uedm.maintenance.malfunction.infrastructure.repository.malfunction.po.MalfunctionClassifyPO">
        select * from malfunction_classify
    </select>

    <update id="saveChangeStatus" >
        update workflow_instance_risk set status =#{newStatus} ,handler =#{handler} where workflow_id =#{workflowId} and status =#{oldStatus}
    </update>

    <select id="selectById" resultType="com.zte.uedm.maintenance.malfunction.infrastructure.repository.malfunction.po.MalfunctionPO">
        select id, workflow_id, code as malfunctionCode, name as malfunctionName, flexible_str4 as reporter, handler, flexible_str1 as malfunctionClassify,
        flexible_str5 as malfunctionHappenTime, status, flexible_int4 as malfunctionSource, flexible_int1 as effectLevel, flexible_int2 as emergencyLevel,
        flexible_int3 as malfunctionLevel, flexible_str2 as position, flexible_str3 as relateOrder,flexible_str6 as positionId,flexible_str7 as relateOrderId, describe, attachment, creator, gmt_create
        from workflow_instance_risk
        where id = #{id}
    </select>

    <select id="selectRiskData" resultType="com.zte.uedm.maintenance.risk.infrastructure.repository.risk.po.RiskDataPO">
        SELECT b.proc_inst_id_ AS workFlowId,task_def_key_ AS STATUS,start_time_ AS operateTime,c.flexible_str5 AS createTime
             <if test="riskSource != null and riskSource.size() > 0">
                 ,c.flexible_int4 AS riskSource
             </if>
             <if test="effectLevel != null and effectLevel.size() > 0">
                 ,c.flexible_int1 AS effectLevel
             </if>
             <if test="emergencyLevel != null and emergencyLevel.size() > 0">
                 ,c.flexible_int2 AS emergencyLevel
             </if>
             <if test="riskLevel != null and riskLevel.size() > 0">
                 ,c.flexible_int3 AS riskLevel
             </if>
        , case when c.flexible_str5 between #{startTimestamp} and #{endTimestamp} then true else false end as addFlag
        FROM (
        SELECT a.*
        FROM (
        SELECT proc_inst_id_,task_def_key_,start_time_
        FROM act_hi_taskinst
        WHERE start_time_ IN
        (
        SELECT MAX(start_time_)
        FROM act_hi_taskinst
        WHERE proc_def_id_ LIKE 'risk_list%' AND (task_def_key_ IN ('cacel','over','executeFirst','executeSecond','hangupSecond','hangupFirst') AND start_time_ &lt;= #{endTimestamp})
        GROUP BY proc_inst_id_)) a
        WHERE (a.start_time_ BETWEEN #{startTimestamp} AND #{endTimestamp})
           OR (a.start_time_ &lt; #{startTimestamp} AND a.task_def_key_ NOT IN ('cacel','over'))
           OR (a.start_time_ &gt; #{endTimestamp} AND a.task_def_key_ NOT IN ('cacel','over'))) b
        INNER JOIN workflow_instance_risk c ON b.proc_inst_id_ = c.workflow_id and c.status != '0'
        WHERE (
            -- 风险单创建时间在查询范围内的记录
            c.flexible_str5 BETWEEN #{startTimestamp} AND #{endTimestamp}
            OR
            -- 风险单创建时间在查询范围外，但当前状态不是终结状态的记录
            (c.flexible_str5 &lt; #{startTimestamp} AND b.task_def_key_ NOT IN ('cacel','over'))
        )
        <where>
            <if test="riskSource != null and riskSource.size() > 0">
                c.flexible_int4 in
                <foreach collection="riskSource" item="rs" open="(" close=")" separator=",">
                    #{rs}
                </foreach>
            </if>
            <if test="effectLevel != null and effectLevel.size() > 0">
                and c.flexible_int1 in
                <foreach collection="effectLevel" item="efl" open="(" close=")" separator=",">
                    #{efl}
                </foreach>
            </if>
            <if test="emergencyLevel != null and emergencyLevel.size() > 0">
                and c.flexible_int2 in
                <foreach collection="emergencyLevel" item="eml" open="(" close=")" separator=",">
                    #{eml}
                </foreach>
            </if>
            <if test="riskLevel != null and riskLevel.size() > 0">
                and c.flexible_int3 in
                <foreach collection="riskLevel" item="rl" open="(" close=")" separator=",">
                    #{rl}
                </foreach>
            </if>
        </where>
        <if test="effectLevel != null or emergencyLevel != null or riskLevel != null">
            <trim prefix=" order by " suffixOverrides=",">
                <if test="riskSource != null and riskSource.size() > 0">
                    c.flexible_int4 asc,
                </if>
                <if test="riskLevel != null and riskLevel.size() > 0">
                    c.flexible_int3 desc,
                </if>
                <if test="effectLevel != null and effectLevel.size() > 0">
                    c.flexible_int1 desc,
                </if>
                <if test="emergencyLevel != null and emergencyLevel.size() > 0">
                    c.flexible_int2 desc,
                </if>
            </trim>
        </if>
    </select>

</mapper>