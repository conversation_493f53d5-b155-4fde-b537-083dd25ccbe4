package com.zte.uedm.maintenance.drill.interfaces.web.beans.topo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * 模糊查询响应 - bean
 *
 * @see com.zte.uedm.configuration.bean.topo.TopoTreeResponseBean;
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TopoTreeResponseBean
{
    /**
     * 拓扑图id
     */
    private String id;
    /**
     * 父节点,根目录下节点值为 “Root”
     */
    private String pid;
    /**
     * 节点名称
     */
    private String name;
    /**
     * 类型，标志节点的类型，不同类型有不同的处理逻辑   "resource"、"topo"
     */
    private String type;

}
