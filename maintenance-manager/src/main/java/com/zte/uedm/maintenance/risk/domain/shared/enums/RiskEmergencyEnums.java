package com.zte.uedm.maintenance.risk.domain.shared.enums;

import java.util.List;

/**
 * <AUTHOR>
 */

public enum RiskEmergencyEnums {
    LOW(0,"低","Low"),
    MIDDLE(1,"中","Middle"),
    HIGH(2,"高","High");

    private Integer code;
    private String cnString;
    private String enString;

    RiskEmergencyEnums(Integer code, String cnString, String enString){
        this.code=code;
        this.cnString=cnString;
        this.enString=enString;
    }

    public Integer getCode(){
        return this.code;
    }

    public static String getCnStringByCode(List<Integer> codeList){
        for (Integer code : codeList) {
            for (RiskEmergencyEnums v : values()) {
                if (v.code.equals(code)) {
                    return v.cnString;
                }
            }
        }
        return "";
    }

    public static String getEnStringByCode(List<Integer> codeList){
        for (Integer code : codeList) {
            for (RiskEmergencyEnums v : values()) {
                if (v.code.equals(code)) {
                    return v.enString;
                }
            }
        }
        return "";
    }
}
