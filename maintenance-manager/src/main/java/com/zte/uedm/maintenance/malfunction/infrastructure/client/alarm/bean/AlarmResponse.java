package com.zte.uedm.maintenance.malfunction.infrastructure.client.alarm.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class AlarmResponse
{
    /**
     * 查询到的告警信息
     */
    private List<Alarm> alarms;

    /**
     * 查询总数
     */
    private Integer totalcount;

    /**
     * 历史告警的id
     */
    private List<Long> ids;

    /**
     * 查询时间戳
     */
    private String timestamp;

    /**
     * 当前所在页面
     */
    private Integer currentpage;

    /**
     * 当前告警统计信息
     */
    private List<SeveritystatisticsBean> statisticsseverities;
}
