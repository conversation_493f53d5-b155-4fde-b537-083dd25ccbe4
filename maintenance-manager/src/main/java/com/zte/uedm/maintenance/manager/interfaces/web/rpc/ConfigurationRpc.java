package com.zte.uedm.maintenance.manager.interfaces.web.rpc;

import com.zte.uedm.common.bean.ResponseBean;
import com.zte.ums.zenap.httpclient.retrofit.annotaion.ReTryFlag;
import com.zte.ums.zenap.httpclient.retrofit.annotaion.ServiceHttpEndPoint;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.List;

@ServiceHttpEndPoint(serviceName = "configuration", serviceVersion = "v1")
@ReTryFlag(flag = true, maxRetryTimes = 5, maxRetryDuration = 60 * 1000L)
public interface ConfigurationRpc
{

    /**
     * 根据fileId得到图片base64串
     *
     * @param fileId
     * @return ResponseBean
     */
    @GET("file-util/picture-base64-by-fileId")
    Call<ResponseBean> getPictureBase64StrByFileId(@Query("fileId") String fileId);

    /**
     * 根据fileIds得到图片base64串
     * @param fileIds
     * @return ResponseBean
     */
    @POST("file-util/picture-base64-by-fileIds")
    Call<ResponseBean> getPictureBase64StrByFileIds(@Body List<String> fileIds);

}
