package com.zte.uedm.maintenance.malfunction.infrastructure.client.alarm.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class AlarmConditionBean {

    /**
     * 告警位置信息
     */
    private List<AlarmPositionPath> positionpaths;
    /**
     * 告警码信息
     */
    private List<AlarmCode> alarmcodeconds;
    /**
     * 告警级别
     */
    private List<Integer> severities;

    private List<String> aids;
    /**
     * 告警类型
     */
    private List<Integer> alarmtypes;
    /**
     * 告警上报时间
     */
    private  AlarmRaisedTime alarmraisedtime;
    /**
     * 确认状态 1表示确认 2表示未确认
     */
    private List<Integer> ackstates;
    /**
     * 告警确认时间
     */
    private AlarmAckTime alarmacktime;
    /**
     * 网元类型
     */
    private List<String> mocs;
    /**
     * 告警可见性
     */
    private List<Integer> visibles;
    /**
     * 告警关系：普通告警0  主告警1  辅告警2  主辅告警3
     */
    private List<Integer> relationflags;
    /**
     * 路径id列表
     */
    private List<String> pathids;
    /**
     *确认用户
     */
    private String ackuser;
    /**
     * 扩展条件
     */
    private Object extensionconds;
    /**
     * 告警注释信息
     */
    private String commenttext;
    /**
     * 清除类型
     */
    private List<Integer> cleartypes;
    /**
     * 告警清除时间
     */
    private AlarmClearedTime alarmclearedtime;
    /**
     * 告警alarmkey
     */
    private String alarmkey;

    private SortInfo sortInfo;
}
