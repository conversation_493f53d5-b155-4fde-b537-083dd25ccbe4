package com.zte.uedm.maintenance.workbench.interfaces.web.dto;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * @FileDesc :
 * <AUTHOR> 10256310
 * @date Date : 2021年05月19日 9:58
 * @Version : 1.0
 */
@Getter
@Setter
@ToString
public class AssetActivitiApprovalResultVo
{
    private String id;

    private String applicationName;

    private String status;

    private Map<String, String> type;

    private String applicant;

    private String applicationTime;

    private List<ValueInfo> attachments;

    private String  description;

    private String  currentProcessor;

    private String gmtModified;

    private String gmtCreate;

    private String planStartTime;

    private String planEndTime;

    private String deliveryMode;

    private String expressTicketNumber;

    private String retirementReason;

    private List<String> informerList;

    /**
     * 预占工单号
     */
    private String preOccupyWorkflowId;

}
