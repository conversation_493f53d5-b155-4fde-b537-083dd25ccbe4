package com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.MaintenancePlanEntity;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.valueobj.PlanIdV;
import com.zte.uedm.maintenance.manager.application.command.InspectPlanStatusCommand;
import com.zte.uedm.maintenance.manager.application.query.MaintenancePlanListQuery;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceplan.model.valueobj.PlanNameV;

import java.util.List;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceplan.po.MaintenancePlanPO;

public interface MaintenancePlanRepository extends IService<MaintenancePlanPO> {

    /**
     * 维保计划名称校验
     */
    MaintenancePlanEntity findMaintenancePlanEntity(PlanNameV name, PlanIdV planId);

    /**
     * 查询维保计划
     */
    List<MaintenancePlanEntity> pagePlanByCondition(MaintenancePlanListQuery vo);

    IPage<MaintenancePlanEntity> pagePlanByCondition(Page<?> page, MaintenancePlanListQuery vo);

    /**
     * 查询历史版本
     */
    List<MaintenancePlanEntity> findHistoryVersionPlan(PlanIdV planId);

    void updateStatusById(InspectPlanStatusCommand inspectPlanStatusDto);

    List<MaintenancePlanPO> selectHistoryVersion(String baseVersionId, String id);

    /**
     * 查询需要生成任务的计划
     */
    List<MaintenancePlanPO> selectNeedGenerateTaskPlan(String tomorrowZeroTime, String nextWeekZeroTime, String nextMonthZeroTime);

    List<MaintenancePlanPO> selectAllEnabledPlan();

    MaintenancePlanEntity getMaxVersionByBaseVersionId(String baseVersionId);

    PlanIdV store(MaintenancePlanEntity planEntity,MaintenancePlanEntity oldPlan);
    PlanIdV store(MaintenancePlanEntity planEntity);

    MaintenancePlanEntity getById(PlanIdV planIdV);

    void deletePlan(List<String> planIdList);
}
