package com.zte.uedm.maintenance.risk.domain.service;

import com.alibaba.fastjson.JSONObject;
import com.zte.uedm.maintenance.risk.application.query.RiskFormQuery;
import com.zte.uedm.maintenance.risk.domain.aggregate.risk.model.RiskReportChartMonthStaticLastDayUnresolved;
import com.zte.uedm.maintenance.risk.infrastructure.common.RiskConstant;
import com.zte.uedm.maintenance.risk.interfaces.web.dto.RiskDataDTO;
import com.zte.uedm.maintenance.risk.domain.aggregate.risk.model.RiskReportChartResponseBean;
import com.zte.uedm.maintenance.risk.domain.aggregate.risk.model.RiskReportChartResponseBeanTampNum;
import com.zte.uedm.maintenance.risk.infrastructure.common.util.GetMonthOrDataByRange;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jetty.util.ajax.JSON;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class RiskReportChartProcessData {
    @Resource
    private GetMonthOrDataByRange getMonthOrDataByRange;

    private static final String MONTH = "month";

    public List<RiskReportChartResponseBean> getRiskReportChart(RiskFormQuery riskFormQuery, List<RiskDataDTO> riskDataDTOList) {

        List<String> rangeList = new ArrayList<>();
        if (null == riskFormQuery.getStatisticalType()
                && getMonthOrDataByRange.getDayBetween(riskFormQuery.getStartTime(),riskFormQuery.getEndTime()).size() > 90){
            log.info("static by month");
            return solveDayMonth(riskDataDTOList,MONTH,riskFormQuery);
        }else if (null == riskFormQuery.getStatisticalType()){
            log.info("static by day");
            return solveDayMonth(riskDataDTOList,"day",riskFormQuery);
        }else if(riskFormQuery.getStatisticalType().equals("day")){
            log.info("static by day");
            return solveDayMonth(riskDataDTOList,"day",riskFormQuery);
        }else if (riskFormQuery.getStatisticalType().equals(MONTH)){
            log.info("static by month");
            return solveDayMonth(riskDataDTOList,MONTH,riskFormQuery);
        }else {
            return null;
        }
    }


    private List<RiskReportChartResponseBean> solveDayMonth( List<RiskDataDTO> riskDataDTOList,String type,RiskFormQuery riskFormQuery ){
        log.info(JSON.toString(riskDataDTOList));
        List<String> rangeList = getMonthOrDataByRange.getDayBetween(riskFormQuery.getStartTime(), riskFormQuery.getEndTime());
        log.info(JSON.toString(rangeList));
        List<RiskReportChartResponseBean> riskReportChartResponseBeanListDay = new ArrayList<>(rangeList.size());
        List<RiskReportChartResponseBean> riskReportChartResponseBeanListMonth = new ArrayList<>(rangeList.size());
        if ("day".equals(type)){
            riskReportChartResponseBeanListDay = Statistics(rangeList,riskDataDTOList,riskReportChartResponseBeanListDay);
            return riskReportChartResponseBeanListDay;
        }else{
            riskReportChartResponseBeanListDay = Statistics(rangeList,riskDataDTOList,riskReportChartResponseBeanListDay);
            List<String> rangeListMonth = getMonthOrDataByRange.getMonthBetween(riskFormQuery.getStartTime(),riskFormQuery.getEndTime());
            log.info(rangeListMonth.toString());
            for (String range : rangeListMonth) {
                new RiskReportChartResponseBeanTampNum();
                RiskReportChartResponseBeanTampNum tNum;
                RiskReportChartResponseBeanTampNum tampNum = new RiskReportChartResponseBeanTampNum();
                RiskReportChartMonthStaticLastDayUnresolved unresolvedLastday = new RiskReportChartMonthStaticLastDayUnresolved();
                for (RiskReportChartResponseBean riskReportChartResponseBean : riskReportChartResponseBeanListDay) {
                    if (!riskReportChartResponseBean.getTimestamp().contains(range)) {
                        continue;
                    }
                    tNum = monStaticNum(riskReportChartResponseBean,range);
                    if ( null == unresolvedLastday.getLastDay()){
                        unresolvedLastday.setLastDay(riskReportChartResponseBean.getTimestamp());
                        unresolvedLastday.setUnresolved(tNum.getNumUnsolved());
                    }else {
                        if (riskReportChartResponseBean.getTimestamp().compareTo(unresolvedLastday.getLastDay()) > 0){
                            unresolvedLastday.setLastDay(riskReportChartResponseBean.getTimestamp());
                            unresolvedLastday.setUnresolved(tNum.getNumUnsolved());
                        }
                    }
                    tampNum.setNumAdd(tampNum.getNumAdd()+tNum.getNumAdd());
                    tampNum.setNumSolved(tampNum.getNumSolved()+tNum.getNumSolved());
                    tampNum.setNumUnsolved(unresolvedLastday.getUnresolved());
                    //log.info(JSONObject.toJSONString(unresolvedLastday));
                }
                RiskReportChartResponseBean riskReportChartResponseBean = new RiskReportChartResponseBean();
                riskReportChartResponseBean.setTimestamp(range);
                riskReportChartResponseBean.setTampNum(tampNum);
                log.info(JSONObject.toJSONString(riskReportChartResponseBean));
                riskReportChartResponseBeanListMonth.add(riskReportChartResponseBean);
            }
            return riskReportChartResponseBeanListMonth;
        }
    }


    public  List<RiskReportChartResponseBean> Statistics(List<String> rangeList,List<RiskDataDTO> riskDataDTOList,
                                                         List<RiskReportChartResponseBean> riskReportChartResponseBeanList){
        for (String range : rangeList) {
            new RiskReportChartResponseBeanTampNum();
            RiskReportChartResponseBeanTampNum tNum;
            RiskReportChartResponseBeanTampNum tampNum = new RiskReportChartResponseBeanTampNum();
            for (RiskDataDTO riskDataDTO : riskDataDTOList) {
                tNum = StaticNum(riskDataDTO,range);
                tampNum.setNumAdd(tampNum.getNumAdd()+tNum.getNumAdd());
                tampNum.setNumSolved(tampNum.getNumSolved()+tNum.getNumSolved());
                tampNum.setNumUnsolved(tampNum.getNumUnsolved()+tNum.getNumUnsolved());
            }
            RiskReportChartResponseBean riskReportChartResponseBean = new RiskReportChartResponseBean();
            riskReportChartResponseBean.setTimestamp(range);
            riskReportChartResponseBean.setTampNum(tampNum);
            riskReportChartResponseBeanList.add(riskReportChartResponseBean);
        }
        return riskReportChartResponseBeanList;
    }

    private RiskReportChartResponseBeanTampNum  StaticNum(RiskDataDTO riskDataDTO,String range){
        int addNum = 0;
        int numSolved = 0;
        int numUnsolved = 0;
        addNum = getAdd(riskDataDTO,range);
        numSolved  = getSolved(riskDataDTO,range);
        numUnsolved =getUnSolved(riskDataDTO,range);
        RiskReportChartResponseBeanTampNum tNum = new RiskReportChartResponseBeanTampNum();
        tNum.setNumAdd(addNum);
        tNum.setNumSolved(numSolved);
        tNum.setNumUnsolved(numUnsolved);
        return tNum;
    }

    private int getAdd(RiskDataDTO riskDataDTO,String range){
        int addNum = 0;
        if (riskDataDTO.getCreateTime().contains(range)){
            addNum++;
        }
        return addNum;
    }
    private int getSolved(RiskDataDTO riskDataDTO,String range){
        boolean solved = Objects.equals(riskDataDTO.getStatus(), RiskConstant.RISK_CANCEL_FORM_KEY) || Objects.equals(riskDataDTO.getStatus(), RiskConstant.RISK_OVER_FORM_KEY);
        int numSolved = 0;
        if (riskDataDTO.getOperateTime().contains(range) && solved){
            numSolved++;
        }
        return numSolved;
    }
    private int getUnSolved(RiskDataDTO riskDataDTO,String range){
        if (riskDataDTO.getCreateTime().split(" ")[0].compareTo(range) > 0 ) {
            return 0;
        }
        boolean solved = Objects.equals(riskDataDTO.getStatus(), RiskConstant.RISK_CANCEL_FORM_KEY) || Objects.equals(riskDataDTO.getStatus(), RiskConstant.RISK_OVER_FORM_KEY);
        int numUnsolved = 0;
        if (riskDataDTO.getOperateTime().split(" ")[0].compareTo(range) >  0 && solved ){
            numUnsolved++;
        }
        if (!solved  ) {
            numUnsolved++;
        }
        return numUnsolved;
    }

    private RiskReportChartResponseBeanTampNum  monStaticNum(RiskReportChartResponseBean riskReportChartResponseBean, String range){
        int addNum = 0;
        int numSolved = 0;
        int unResolved = 0;
        if (riskReportChartResponseBean.getTimestamp().contains(range)){
            addNum = riskReportChartResponseBean.getTampNum().getNumAdd();
            numSolved = riskReportChartResponseBean.getTampNum().getNumSolved();
            unResolved = riskReportChartResponseBean.getTampNum().getNumUnsolved();
        }
        RiskReportChartResponseBeanTampNum tNum = new RiskReportChartResponseBeanTampNum();
        tNum.setNumAdd(addNum);
        tNum.setNumSolved(numSolved);
        tNum.setNumUnsolved(unResolved);
        return tNum;
    }

}
