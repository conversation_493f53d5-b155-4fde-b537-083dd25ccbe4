package com.zte.uedm.maintenance.malfunction.infrastructure.common;


public class GlobalConst {
    /**
     * 中文
     */
    public static final String LANGUAGE_IS_CHINESE_CN = "zh_CN";
    public static final String LANGUAGE_IS_EN_US = "en_US";

    /**
     * 故障单通知类型
     */
    public static final String NOTHING="nothing";
    public static final String SMS="sms";
    public static final String EMAIL="email";
    public static final String SMSANDEMAIL="smsAndEmail";
    public static final String TOPIC="{\"en_US\":\"<Work order processing notice>\",\"zh_CN\":\"<工单处理通知>\"}";
    public static final String DEALMES="{\"en_US\":\"<Work order processing notice> Malfunction order:%s need deal,Submitter :%s ,Submission time: %s\",\"zh_CN\":\"<工单处理通知> 故障单:%s 待您处理，提交人：%s,提交时间：%s\"}";
    public static final String COMPLETEMES="{\"en_US\":\"<Work order complete notice>Malfunction order:%s is %s\",\"zh_CN\":\"<工单完成通知>故障单:您提交的%s已%s。\"}";
    /**
     * 故障单通用类型
     */
    public static final String MALFUNCTION="MALFUNCTION";

    /**
     * 错误提示
     */
    public static final String DATA_NOT_EXIST = "{\"en-US\":\"data not exit\",\"zh-CN\":\"数据已经不存在\"}";
    public static final String DATA_CHANGE = "{\"en-US\":\"data change,please refresh!\",\"zh-CN\":\"数据已改变，请刷新\"}";
    public static final String STATUS_NOT_EXIST="{\"en-US\":\"deal type not exit!\",\"zh-CN\":\"流程处理类型不存在！\"}";
    public static final String NAME_EXIST="{\"en-US\":\"name exit!\",\"zh-CN\":\"名称已经存在！\"}";
    public static final String FLOW_NOT_EXIST="{\"en-US\":\"flow not exit!\",\"zh-CN\":\"流程不存在！\"}";
    public static final String PERMISSION="{\"en-US\":\"deal type is not permission!\",\"zh-CN\":\"没有这个处理权限！\"}";
    public static final String HAVE_TRANSFERRED="{\"en-US\":\"have transferred!\",\"zh-CN\":\"单据已经转换！\"}";
    public static final String TASK_EXIST="{\"en-US\":\"The task has been transferred to a trouble ticket!\",\"zh-CN\":\"该任务已转过故障单!\"}";
    public static final String RISK_TASK_EXIST="{\"en-US\":\"The task has been transferred to a risk ticket!\",\"zh-CN\":\"该任务已转过风险单!\"}";
    /**
     * 流程处理数据
     */
    public static final Integer NOT_CHANGE = 0;
    public static final Integer REVOKE = -1;

    /**
     * 流程模板参数
     */
    public static final String MALFUNCTION_TEMPLATE_KEY = "malfunction";

    public static final String PREFIX_MOC = "r32.uedm.";

    /**
     * 故障单处理流程类型
     */
    public static final String TICKET_FOR_CREATED="Initiated";

    public static final String TICKET_FOR_PENDING="ToBeHandled";

    public static final String TICKET_FOR_HANDLED="Handled";


    /**
     * 故障通知单导出文件名与sheet名
     */
    public static final String FILE_NAME_MALFUNCTION_INITIATED = "{\"zh_CN\":\"工单管理_故障单_我发起的\",\"en_US\":\"WorkOrderManagement_TroubleTicket_Initiated\"}";
    public static final String FILE_NAME_MALFUNCTION_TOBEHANDLED = "{\"zh_CN\":\"工单管理_故障单_待我处理\",\"en_US\":\"WorkOrderManagement_TroubleTicket_ToBeHandled\"}";
    public static final String FILE_NAME_MALFUNCTION_HANDLED = "{\"zh_CN\":\"工单管理_故障单_我已处理\",\"en_US\":\"WorkOrderManagement_TroubleTicket_Handled\"}";
    public static final String FILE_NAME_MALFUNCTION_ALLTICKET = "{\"zh_CN\":\"工单管理_故障单_所有工单\",\"en_US\":\"WorkOrderManagement_TroubleTicket_AllTicket\"}";
    public static final String SHEET_NAME_MALFUNCTION = "{\"zh_CN\":\"故障单\",\"en_US\":\"TroubleTicket\"}";

    /**
     * 风险单导出文件名与sheet名
     */
    public static final String FILE_NAME_RISK_INITIATED = "{\"zh_CN\":\"工单管理_风险单_我发起的\",\"en_US\":\"WorkOrderManagement_RiskTicket_Initiated\"}";
    public static final String FILE_NAME_RISK_TOBEHANDLED = "{\"zh_CN\":\"工单管理_风险单_待我处理\",\"en_US\":\"WorkOrderManagement_RiskTicket_ToBeHandled\"}";
    public static final String FILE_NAME_RISK_HANDLED = "{\"zh_CN\":\"工单管理_风险单_我已处理\",\"en_US\":\"WorkOrderManagement_RiskTicket_Handled\"}";
    public static final String FILE_NAME_RISK_ALLTICKET = "{\"zh_CN\":\"工单管理_风险单_所有工单\",\"en_US\":\"WorkOrderManagement_RiskTicket_AllTicket\"}";
    public static final String SHEET_NAME_RISK = "{\"zh_CN\":\"风险单\",\"en_US\":\"RiskTicket\"}";


}
