package com.zte.uedm.maintenance.duty.interfaces.web.dto;

import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Slf4j
public class HandoverConditionDto {
    private String deliverName;
    private String heritageName;
    private Integer status;
    //当前用户
    @NotNull
    private String userName;
    @Min(value = 1, message = "pageNo must greater than 1")
    private Integer pageNo;
    @Min(value = 1, message = "pageSize must greater than 1")
    private Integer pageSize;
}
