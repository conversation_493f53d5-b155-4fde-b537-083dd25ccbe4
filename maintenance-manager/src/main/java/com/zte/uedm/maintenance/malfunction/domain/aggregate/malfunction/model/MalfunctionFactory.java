package com.zte.uedm.maintenance.malfunction.domain.aggregate.malfunction.model;

import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.maintenance.malfunction.domain.aggregate.malfunction.model.value.MalfunctionV;
import com.zte.uedm.maintenance.malfunction.domain.aggregate.malfunction.repository.MalfunctionRepository;
import com.zte.uedm.maintenance.malfunction.domain.shared.enums.StatusEnums;
import com.zte.uedm.maintenance.malfunction.infrastructure.common.GlobalConst;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.LoginHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 */
public class MalfunctionFactory {
    private MalfunctionRepository malfunctionRepository;

    private DateTimeService dateTimeService;

    public MalfunctionFactory(MalfunctionRepository malfunctionRepository,DateTimeService dateTimeService){
        this.malfunctionRepository=malfunctionRepository;
        this.dateTimeService=dateTimeService;
    }

    public Malfunction createMalFunction(MalfunctionV malfunctionV) throws UedmException {
        //如果存在id判断id所对应的状态是否是拟制状态
        checkParameter(malfunctionV);
        Malfunction malfunction=new Malfunction();
        BeanUtils.copyProperties(malfunctionV,malfunction);
        if(StringUtils.isEmpty(malfunction.getId())){
            String loginUserName = LoginHelper.getLoginUserName();
            String currentTime = dateTimeService.getCurrentTime();
            malfunction.setId(UUID.randomUUID().toString());
            malfunction.setGmtCreate(currentTime);
            malfunction.setCreator(loginUserName);
        }
        malfunction.setStatus(StatusEnums.PENDING.getCode());
        return malfunction;
    }

    public Malfunction createMalFunctionFromAlarm(MalfunctionV malfunctionV) throws UedmException {
        //如果存在id判断id所对应的状态是否是拟制状态
        //checkParameter(malfunctionV);
        Malfunction malfunction=new Malfunction();
        BeanUtils.copyProperties(malfunctionV,malfunction);

        String currentTime = dateTimeService.getCurrentTime();
        malfunction.setId(UUID.randomUUID().toString());
        malfunction.setGmtCreate(currentTime);

        malfunction.setStatus(StatusEnums.PENDING.getCode());
        return malfunction;
    }

    public Malfunction temporaryMalfunction(MalfunctionV malfunctionV) throws UedmException {
        //如果存在id判断id所对应的状态是否是拟制状态
        checkParameter(malfunctionV);
        Malfunction malfunction=new Malfunction();
        BeanUtils.copyProperties(malfunctionV,malfunction);
        if(StringUtils.isEmpty(malfunction.getId())){
            String loginUserName = LoginHelper.getLoginUserName();
            String currentTime = dateTimeService.getCurrentTime();
            malfunction.setId(UUID.randomUUID().toString());
            malfunction.setGmtCreate(currentTime);
            malfunction.setCreator(loginUserName);
        }
        malfunction.setStatus(StatusEnums.DRAFT.getCode());
        return malfunction;
    }


    public void checkParameter(MalfunctionV malfunctionV) throws UedmException {
        if(StringUtils.isNotEmpty(malfunctionV.getId())){
            Malfunction malfunction=malfunctionRepository.findById(malfunctionV.getId());
            if(malfunction==null){
                throw new UedmException(-20,GlobalConst.DATA_NOT_EXIST);
            }
            if(!StatusEnums.DRAFT.getCode().equals(malfunction.getStatus())){
                throw new UedmException(-30,GlobalConst.DATA_CHANGE);
            }
        }
        //校验是否重名
        if(!malfunctionRepository.checkName(malfunctionV.getId(),malfunctionV.getMalfunctionName())){
            throw new UedmException(-10,GlobalConst.NAME_EXIST);
        }
        //检验是否关联id已存在
        if(StringUtils.isNotBlank(malfunctionV.getRelateOrderId())&&!malfunctionRepository.findByRelateOrderId(malfunctionV.getRelateOrderId(),malfunctionV.getId())){
            throw new UedmException(-10,GlobalConst.HAVE_TRANSFERRED);
        }
    }
}
