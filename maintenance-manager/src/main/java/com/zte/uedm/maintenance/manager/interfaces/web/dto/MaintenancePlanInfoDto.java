package com.zte.uedm.maintenance.manager.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 */

@Getter
@Setter
@ToString
public class MaintenancePlanInfoDto {
    /**
     * 维保计划表名称
     */
    private String name;

    /**
     * 维保计划类型1日常维保 2临时维保 3健康检查
     */
    private Integer maintenancePlanType;

    /**
     * 责任部门
     */
    private String responsibleDepartment;

    /**
     * 责任人
     */
    private String responsiblePerson;

    /**
     * 执行部门
     */
    private String executeDepartment;

    /**
     * 执行人
     */
    private String executor;

    /**
     * 维保计划开始时间
     */
    private String planBeginTime;

    /**
     * 维保计划结束时间
     */
    private String planEndTime;

    /**
     * 提醒方式 1无  2短信 3邮件
     */
    private Integer reminderMode;

    /**
     * 维保计划表版本
     */
    private Integer version;
}
