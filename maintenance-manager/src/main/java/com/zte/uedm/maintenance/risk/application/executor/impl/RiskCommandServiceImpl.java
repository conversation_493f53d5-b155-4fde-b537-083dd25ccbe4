package com.zte.uedm.maintenance.risk.application.executor.impl;

import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.maintenance.common.I18nService;
import com.zte.uedm.maintenance.malfunction.application.assembler.MalfunctionValueAssembler;
import com.zte.uedm.maintenance.malfunction.application.command.MalfunctionCommand;
import com.zte.uedm.maintenance.malfunction.application.command.MalfunctionFlowCommand;
import com.zte.uedm.maintenance.malfunction.application.command.MalfunctionStatusCommand;
import com.zte.uedm.maintenance.malfunction.domain.aggregate.malfunction.model.Malfunction;
import com.zte.uedm.maintenance.malfunction.domain.aggregate.malfunction.model.value.MalfunctionV;
import com.zte.uedm.maintenance.malfunction.infrastructure.common.GlobalConst;
import com.zte.uedm.maintenance.malfunction.infrastructure.common.util.EmailUtil;
import com.zte.uedm.maintenance.malfunction.infrastructure.common.util.ResponseUtils;
import com.zte.uedm.maintenance.malfunction.infrastructure.common.util.SmsUtil;
import com.zte.uedm.maintenance.manager.infrastructure.client.user.impl.UserServiceRpcImpl;
import com.zte.uedm.maintenance.risk.application.executor.RiskActivitiService;
import com.zte.uedm.maintenance.risk.application.executor.RiskCommandService;
import com.zte.uedm.maintenance.risk.domain.aggregate.risk.model.RiskFactory;
import com.zte.uedm.maintenance.risk.domain.aggregate.risk.repository.RiskRepository;
import com.zte.uedm.maintenance.risk.domain.service.RiskFlowDealServiceImpl;
import com.zte.uedm.maintenance.risk.domain.service.RiskReminderService;
import com.zte.uedm.maintenance.risk.domain.specification.RiskSpecification;
import com.zte.uedm.maintenance.risk.infrastructure.common.util.RiskLogUtils;
import com.zte.uedm.maintenance.workflow.activiti.bean.HistoryOperationBean;
import com.zte.uedm.maintenance.workflow.activiti.service.MalfunctionActivitiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.zte.uedm.maintenance.risk.infrastructure.common.RiskConstant.*;


@Service
@Slf4j
public class RiskCommandServiceImpl implements RiskCommandService {

    @Resource
    private RiskRepository riskRepository;

    @Resource
    private DateTimeService dateTimeService;

    @Resource
    private RiskLogUtils riskLogUtils;

    @Resource
    private RiskActivitiService activitiService;

    @Resource
    private I18nService i18nService;

    @Resource
    private EmailUtil emailUtil;

    @Resource
    private SmsUtil smsUtil;


    @Resource
    private ConfigService configService;

    @Resource
    private UserServiceRpcImpl userServiceRpc;


    @Override
    public void deleteById(String id,String lang) throws UedmException {
        RiskSpecification riskSpecification= new RiskSpecification(riskRepository,activitiService);
        Malfunction malfunction=riskSpecification.deleteCheck(id, lang);
        //删除操作
        riskRepository.remove(id);
        //记录日志
        riskLogUtils.sendMalfunctionDeleteLog(malfunction);
    }

    @Override
    public Boolean add(MalfunctionCommand malfunctionCommand) throws UedmException {
        MalfunctionV malfunctionV = MalfunctionValueAssembler.convertToV(malfunctionCommand,i18nService);
        //创建风险单
        RiskFactory riskFactory= new RiskFactory(riskRepository,dateTimeService);
        Malfunction malfunction= riskFactory.createMalFunction(malfunctionV);
        //创建流程获取流程id,如果已经存在流程id则调用提交,不存在流程id则需要调用暂存和提交接口
        RiskFlowDealServiceImpl dealService= new RiskFlowDealServiceImpl(activitiService);
        log.info(malfunction.toCNString().replace(MALFUNCTION_VALUE_ZH, RISK_VALUE_ZH).replace(MALFUNCTION_ALARM_ZH,RISK_FIND_ZH)
                .replace(MALFUNCTION_CREATE_CH,RISK_CREATE_CH));
        malfunction= dealService.addFlowDeal(malfunction);
        //保存风险单
        riskRepository.store(malfunction);
        //发送邮件
        List<HistoryOperationBean> historyOperationBeans= dealService.getTimelineByProcessInstanceId(malfunction.getWorkflowId());
        //发送邮件或短信进行通知
        RiskReminderService riskReminderService= new RiskReminderService(emailUtil,smsUtil,configService,userServiceRpc);
        riskReminderService.manualReminder(malfunction, historyOperationBeans);
        //发送日志
        riskLogUtils.sendRiskAddLog(malfunction);
        return true;
    }

    @Override
    public Boolean temporary(MalfunctionCommand malfunctionCommand) throws UedmException {
        MalfunctionV malfunctionV= MalfunctionValueAssembler.convertToV(malfunctionCommand, i18nService);
        //创建故障单
        RiskFactory riskFactory= new RiskFactory(riskRepository, dateTimeService);
        Malfunction malfunction= riskFactory.temporaryMalfunction(malfunctionV);
//        //如果没有流程单id则创建流程单id
//        if (StringUtils.isBlank(malfunctionCommand.getWorkflowId()))
//        {
//            MalfunctionFlowCommand malfunctionFlowCommand = new MalfunctionFlowCommand();
//            malfunctionFlowCommand.setDealType(FlowDealTypeEnums.TEMP.getCode());
//            malfunctionFlowCommand.setHandler(malfunctionFlowCommand.getCurrentHandler());
//            RiskFlowDealServiceImpl dealService = new RiskFlowDealServiceImpl(activitiService);
//            String workflowId = dealService.flowDeal(malfunctionFlowCommand);
//            malfunction.setWorkflowId(workflowId);
//        }
        //保存故障单
        riskRepository.store(malfunction);
        //发送日志
        riskLogUtils.sendMalfunctionTemporaryLog(malfunction);
        return true;
    }
    @Override
    public ResponseBean changeStatus(MalfunctionStatusCommand malfunctionStatusCommand) throws UedmException {
        //检查状态是否能改变
        RiskSpecification riskSpecification=new RiskSpecification(riskRepository, activitiService);
        riskSpecification.changeStatusCheck(malfunctionStatusCommand.getWorkflowId(), malfunctionStatusCommand.getOldStatus(),
                malfunctionStatusCommand.getNewStatus(),malfunctionStatusCommand.getHandler());
        riskRepository.saveChangeStatus(malfunctionStatusCommand.getWorkflowId(), malfunctionStatusCommand.getOldStatus(),
                malfunctionStatusCommand.getNewStatus(),malfunctionStatusCommand.getHandler());
        return ResponseUtils.getNormalResponseBean(0,null,1);
    }

    @Override
    public ResponseBean dealFlow(MalfunctionFlowCommand malfunctionFlowCommand) throws UedmException {
        RiskSpecification riskSpecification=new RiskSpecification(riskRepository, activitiService);
        //检查数据
        Malfunction malfunction=riskSpecification.dealFlowCheck(malfunctionFlowCommand.getId(), malfunctionFlowCommand.getDealType(), malfunctionFlowCommand.getCurrentHandler());
        //按照不同类型去调用不同流程方法
        RiskFlowDealServiceImpl dealService= new RiskFlowDealServiceImpl(activitiService);
        activitiService.checkHangupIng(malfunctionFlowCommand);
        String dealFlowReturn=dealService.flowDeal(malfunctionFlowCommand);
        //处理人处理
        riskSpecification.dealHandler(malfunction, malfunctionFlowCommand.getDealType(), malfunctionFlowCommand.getHandler(),dealFlowReturn);
        //保存故障单
        riskRepository.store(malfunction);
        List<HistoryOperationBean> historyOperationBeans=dealService.getTimelineByProcessInstanceId(malfunction.getWorkflowId());
        //发送邮件或短信进行通知
        RiskReminderService riskReminderService= new RiskReminderService(emailUtil,smsUtil,configService,userServiceRpc);
        riskReminderService.manualReminder(malfunction,historyOperationBeans);
        riskLogUtils.sendDealFlowLog(malfunction,malfunctionFlowCommand);
        return ResponseUtils.getNormalResponseBean(0,null,1);
    }

    @Override
    public ResponseBean reminder(String id) throws UedmException {
        //找到处理人按照配置发送催单通知
        Malfunction malfunction= riskRepository.findById(id);
        if(malfunction==null){
            throw new UedmException(-1, GlobalConst.DATA_NOT_EXIST);
        }
        RiskReminderService riskReminderService= new RiskReminderService(emailUtil,smsUtil,configService,userServiceRpc);
        //只有待处理和处理中和挂起的可以催单
        riskReminderService.checkStatus(malfunction);
        //流程检查
        RiskFlowDealServiceImpl dealService= new RiskFlowDealServiceImpl(activitiService);
        List<HistoryOperationBean> historyOperationBeans= dealService.getTimelineByProcessInstanceId(malfunction.getWorkflowId());
        //发送邮件进行催单
        riskReminderService.manualReminder(malfunction,historyOperationBeans);
        return ResponseUtils.getNormalResponseBean(0,null,1);
    }

}
