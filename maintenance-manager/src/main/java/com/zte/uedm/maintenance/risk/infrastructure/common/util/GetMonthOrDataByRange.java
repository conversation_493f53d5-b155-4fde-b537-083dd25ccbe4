package com.zte.uedm.maintenance.risk.infrastructure.common.util;

import com.zte.uedm.common.enums.DatabaseExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.min;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class GetMonthOrDataByRange {
    SimpleDateFormat simpleDateFormatDay=new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat simpleDateFormatMon = new SimpleDateFormat("yyyy-MM");//格式化为年月

    //根据时间范围获取所有天数
    public List<String> getDayBetween(String startDateStr, String endDateStr) {
        ArrayList<String> result = new ArrayList<String>();
        Calendar start = Calendar.getInstance();
        Calendar end = Calendar.getInstance();
        try {
            start.setTime(simpleDateFormatDay.parse(startDateStr));
            end.setTime(simpleDateFormatDay.parse(endDateStr));
//            end.add(Calendar.DAY_OF_MONTH,-1);

            Calendar curr = start;
            while (curr.before(end)) {
                result.add(simpleDateFormatDay.format(curr.getTime()));
                curr.add(Calendar.DAY_OF_MONTH, 1);
            }
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
        }

        result.add(simpleDateFormatDay.format(end.getTime()));
        return result.stream().distinct().collect(Collectors.toList());
    }
    //根据时间范围获取所有月数
    public List<String> getMonthBetween(String startDate, String endDate){
        ArrayList<String> result = new ArrayList<String>();
        try {
            Calendar min = Calendar.getInstance();
            Calendar max = Calendar.getInstance();
            min.setTime(simpleDateFormatMon.parse(startDate));
            min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);

            max.setTime(simpleDateFormatMon.parse(endDate));
            max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);

            Calendar curr = min;
            while (curr.before(max)) {
                result.add(simpleDateFormatMon.format(curr.getTime()));
                curr.add(Calendar.MONTH, 1);
            }
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
        }

        return result;
    }
}
