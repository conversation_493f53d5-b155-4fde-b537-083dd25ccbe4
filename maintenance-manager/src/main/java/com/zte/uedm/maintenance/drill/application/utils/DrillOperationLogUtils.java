package com.zte.uedm.maintenance.drill.application.utils;

import com.zte.log.filter.UserThreadLocal;
import com.zte.uedm.common.bean.GlobalizationInfo;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.kafka.producer.service.MsgSenderService;
import com.zte.uedm.maintenance.drill.infrastructure.repository.pojo.DrillTemplatePojo;
import com.zte.uedm.maintenance.drill.interfaces.web.beans.DrillDeleteTemplateLogBean;
import com.zte.uedm.maintenance.drill.interfaces.web.beans.DrillOperationLogBean;
import com.zte.uedm.maintenance.drill.interfaces.web.dto.DrillRecordDetailDto;
import com.zte.uedm.maintenance.drill.interfaces.web.dto.DrillTemplateDto;
import com.zte.uedm.maintenance.drill.interfaces.web.enums.OperateTypeEnum;
import com.zte.uedm.maintenance.manager.infrastructure.common.bean.OperationLogBean;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.I18nUtils;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.LoginHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class DrillOperationLogUtils {

    private final static String TOPIC = "IMOP_CMA_LOG";

    private static final String RANK = "operlog_rank_normal";

    private static final String TYPE = "operation";

    private static final String APP_MODULE_ZH = "应急演练";

    private static final String APP_MODULE_EN = "Emergency drill";

    private final static String CREATOR = "Creator:";

    private final static String GMT_CREATE = "Create Time:";

    private final static String UPDATER = "Updater:";

    private final static String GMT_MODIFIED = "Update Time:";

    private final static String LOG_SUCCESS = "log_success";

    private final static String LOG_FAIL = "log_fail";

    @Resource
    private DateTimeService dateTimeService;

    @Resource
    private MsgSenderService msgSenderService;

    @Resource
    private I18nUtils i18nUtils;

    @Resource
    private JsonService jsonService;

    public void sendDrillTemplateDeleteSuccessLog(DrillDeleteTemplateLogBean logBean) {
        String beginTime = dateTimeService.getCurrentTime();
        StringBuilder cnStrBuilder = new StringBuilder();
        StringBuilder enStrBuilder = new StringBuilder();

        cnStrBuilder.append(logBean.getBriefStringCn());
        enStrBuilder.append(logBean.getBriefStringEn());

        if(!CollectionUtils.isEmpty(logBean.getDeletedTemplatePojoList())) {
            List<DrillTemplatePojo> templatePojoList = logBean.getDeletedTemplatePojoList();
            cnStrBuilder.append("已删除").append(templatePojoList.size()).append("个应急演练模板===>");
            enStrBuilder.append("Deleted ").append(templatePojoList.size()).append(" emergency drill template(s)===>");
            for (DrillTemplatePojo templatePojo : templatePojoList) {
                cnStrBuilder.append("【模板名称：").append(templatePojo.getTemplateName())
                        .append("（id=").append(templatePojo.getTemplateId()).append("），")
                        .append("计划演练时长：").append(templatePojo.getPlanDuration()).append("】 ");

                enStrBuilder.append("Template name:").append(templatePojo.getTemplateName())
                        .append("(id=").append(templatePojo.getTemplateId()).append("), ")
                        .append("Emergency drill duration:").append(templatePojo.getPlanDuration()).append("] ");
            }
        }

        if(!CollectionUtils.isEmpty(logBean.getIgnoredTemplatePojoList())) {
            List<DrillTemplatePojo> templatePojoList = logBean.getIgnoredTemplatePojoList();
            cnStrBuilder.append("已忽略").append(templatePojoList.size()).append("个正在使用中的应急演练模板===>");
            enStrBuilder.append(templatePojoList.size()).append(" emergency drill template(s) in use ignored ===>");
            for (DrillTemplatePojo templatePojo : templatePojoList) {
                cnStrBuilder.append("【模板名称：").append(templatePojo.getTemplateName())
                        .append("（id=").append(templatePojo.getTemplateId()).append("），")
                        .append("计划演练时长：").append(templatePojo.getPlanDuration()).append("】 ");

                enStrBuilder.append("Template name:").append(templatePojo.getTemplateName())
                        .append("(id=").append(templatePojo.getTemplateId()).append("), ")
                        .append("Emergency drill duration:").append(templatePojo.getPlanDuration()).append("] ");
            }
        }

        //详情
        String detail = getDetail(cnStrBuilder.toString(), enStrBuilder.toString());
        //操作
        String operation = getI18nStr("删除应急演练模板", "Delete emergency drill template");
        //发送
        sendSuccessKafkaMsg(beginTime, operation, OperateTypeEnum.DELETE, detail);

    }

    public void sendDrillTemplateDeleteFailedLog(UedmException exp) {
        String beginTime = dateTimeService.getCurrentTime();
        String operation = getI18nStr("删除应急演练模板", "Delete emergency drill template");
        String reasonI18nStr = exp.getErrorData();
        String reasonCN = i18nUtils.getMapFieldByLanguageOption(reasonI18nStr,"zh-CN");
        String reasonEn = i18nUtils.getMapFieldByLanguageOption(reasonI18nStr,"en-US");
        String detail = getDetail("删除应急演练模板失败：" + reasonCN, "Delete emergency drill template failed:" + reasonEn);
        sendFailedKafkaMsg(beginTime, operation, OperateTypeEnum.DELETE, detail, reasonI18nStr);
    }


    public void sendDrillPreviewLogExport(DrillRecordDetailDto reportVo, String currentTime, String result)
    {
        //详情
        String detail = getDetail(
                "演练记录Id：" + reportVo.getRecordId()+ "," + "演练模板名称：" + reportVo.getTemplateName() + ","
                        + "演练起止时间：" + reportVo.getStartTime()
                        + "——" + reportVo.getEndTime() + ","+"实际/计划演练时长:" +reportVo.getActualDuration()+ "," + "演练结果：" + reportVo.getTemplateId()+ " ",
                "Drill Record Id：" + reportVo.getRecordId()+ "," + "Drill Template Name：" + reportVo.getTemplateName() + ","
                        + "Drill Start-End Time：" + reportVo.getStartTime()
                        + "——" + reportVo.getEndTime() + ","+"Actual/Planned Drill Duration:" +reportVo.getActualDuration()+ "," + "Drill Result：" + reportVo.getTemplateId()+ " "
        );
        //操作
        String operation = getI18nStr("导出演练记录报告", "Export Drill Record Report");
        //发送

        sendKafkaMsg(currentTime, operation, detail, result, "");
    }


    public void sendDrillTemplateAddLog(DrillTemplateDto templatePojo) {
        String beginTime = dateTimeService.getCurrentTime();
        try {
            StringBuilder cnStrBuilder = new StringBuilder();
            StringBuilder enStrBuilder = new StringBuilder();
            cnStrBuilder.append("创建应急演练模板：");
            enStrBuilder.append("Add emergency drill template:");

            cnStrBuilder.append("模板名称：").append(templatePojo.getTemplateName()).append("，")
                    .append("计划演练时长：").append(templatePojo.getPlanDuration()).append("，")
                    .append("创建人：").append(templatePojo.getCreator()).append("，")
                    .append("创建时间：").append(invertDateTimeToString(new Date())).append(".");

            enStrBuilder.append("Template name:").append(templatePojo.getTemplateName()).append(", ")
                    .append("Emergency drill duration:").append(templatePojo.getPlanDuration()).append(", ")
                    .append(CREATOR).append(templatePojo.getCreator()).append(", ")
                    .append(GMT_CREATE).append(invertDateTimeToString(new Date())).append(".");

            //详情
            String detail = getDetail(cnStrBuilder.toString(), enStrBuilder.toString());
            //操作
            String operation = getI18nStr("新增应急演练模板", "Add emergency drill template");
            //发送
            sendSuccessKafkaMsg(beginTime, operation, OperateTypeEnum.ADD, detail);

        } catch (Exception e) {
            log.error("sendDrillTemplateDeleteSuccessLog error.....reason=", e);
        }
    }

    public void sendDrillTemplateUpdateLog(DrillTemplateDto templatePojo) {
        String beginTime = dateTimeService.getCurrentTime();
        try {
            StringBuilder cnStrBuilder = new StringBuilder();
            StringBuilder enStrBuilder = new StringBuilder();
            cnStrBuilder.append("更新应急演练模板：");
            enStrBuilder.append("Add emergency drill template:");

            cnStrBuilder.append("模板名称：").append(templatePojo.getTemplateName()).append("，")
                    .append("计划演练时长：").append(templatePojo.getPlanDuration()).append("，")
                    .append("创建人：").append(templatePojo.getCreator()).append("，")
                    .append("更新时间：").append(invertDateTimeToString(new Date())).append(".");

            enStrBuilder.append("Template name:").append(templatePojo.getTemplateName()).append(", ")
                    .append("Emergency drill duration:").append(templatePojo.getPlanDuration()).append(", ")
                    .append(CREATOR).append(templatePojo.getCreator()).append(", ")
                    .append(GMT_MODIFIED).append(invertDateTimeToString(new Date())).append(".");

            //详情
            String detail = getDetail(cnStrBuilder.toString(), enStrBuilder.toString());
            //操作
            String operation = getI18nStr("更新应急演练模板", "Add emergency drill template");
            //发送
            sendSuccessKafkaMsg(beginTime, operation, OperateTypeEnum.UPDATE, detail);

        }catch (Exception e) {
            log.error("sendDrillTemplateDeleteSuccessLog error.....reason=", e);
        }
    }

    public String invertDateTimeToString(Date timeTag) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return timeTag == null ? null : formatter.format(timeTag);
    }

    public String getI18nStr(String zh, String en) {
        return String.format("{\"zh_CN\":\"%s\",\"en_US\":\"%s\"}", zh, en);
    }

    public String getDetail(String zh, String en) {
        GlobalizationInfo globalInfoDetail = new GlobalizationInfo();
        globalInfoDetail.setEn_US(en);
        globalInfoDetail.setZh_CN(zh);

        String result = "";
        try {
            result = jsonService.objectToJson(globalInfoDetail);
        } catch (UedmException e) {
            log.warn("object to json error...", e);
        }
        return result;
    }
    public void sendSuccessKafkaMsg(String beginTime, String operation, OperateTypeEnum operateType, String details){
        sendKafkaMsg(beginTime, operation, operateType, details, LOG_SUCCESS, "");
    }

    public void sendFailedKafkaMsg(String beginTime, String operation, OperateTypeEnum operateType,
                                    String details, String failReasonI18n){
        sendKafkaMsg(beginTime, operation, operateType, details, LOG_FAIL, failReasonI18n);
    }

    public void sendKafkaMsg(String beginTime, String operation, OperateTypeEnum operateType,
                             String details, String result, String failReasonI18n) {
        DrillOperationLogBean operationLogBean = new DrillOperationLogBean();
        operationLogBean.setOperateType(operateType.getCode());
        sendKafkaMsg(operationLogBean, beginTime, operation, details, result, failReasonI18n);
    }

    public void sendKafkaMsg(String beginTime, String operation, String details, String result, String failReasonI18n) {
        sendKafkaMsg(new OperationLogBean(), beginTime, operation, details, result, failReasonI18n);
    }

    public void sendKafkaMsg(OperationLogBean logBean, String beginTime, String operation,
                             String details, String result, String failReasonI18n) {
        try {
            String appModuleNameI18n = getI18nStr(APP_MODULE_ZH, APP_MODULE_EN);
            String endTime = dateTimeService.getCurrentTime();
            logBean.setRank(RANK);
            logBean.setType(TYPE);
            String connectMode = UserThreadLocal.getLoginType();
            logBean.setConnectMode(connectMode);
            logBean.setHostname(LoginHelper.getRemoteHost());
            logBean.setUserName(LoginHelper.getLoginUserName());
            logBean.setLogStartDate(beginTime);
            logBean.setLogEndDate(endTime);
            logBean.setOperation(operation);
            logBean.setDescriptionInfo(operation);
            logBean.setOperateResult(result);
            logBean.setDetail(details);
            logBean.setFailReason(failReasonI18n);
            logBean.setAppModule(appModuleNameI18n);
            log.info("=========begin send msg to kafka,data:{}", logBean);
            String json = jsonService.objectToJson(logBean);
            log.info("=========begin send msg to kafka,json data:{}", json);
            msgSenderService.sendMsgAsync(TOPIC, json);
        } catch (Exception e) {
            log.warn("record log error", e);
        }
    }

    public void sendDrillStartLog(String templateName,boolean result) {
        //详情
        String detail = getDetail(
                "演练模板名称：" + templateName + ","
                        + "演练开始时间：" + dateTimeService.getCurrentTime() +
                        " ",
                "Drill Template Name：" + templateName + "," + "Drill start time :" + dateTimeService.getCurrentTime() + " "

        );
        //操作
        String operation = getI18nStr("开始演练", "Start drill");
        //发送
        sendKafkaMsg(dateTimeService.getCurrentTime(), operation, detail, LOG_SUCCESS, "");

    }

    public void sendDrillCancelLog(String templateName, boolean result) {
        //详情
        String detail = getDetail(
                "演练模板名称：" + templateName + ","
                        + "结束开始时间：" + dateTimeService.getCurrentTime() +
                        " ",
                "Drill Template Name：" + templateName + "," + "Drill end time :" + dateTimeService.getCurrentTime() + " "

        );
        //操作
        String operation = getI18nStr("终止演练", "Interrupt drill");
        //发送
        sendKafkaMsg(dateTimeService.getCurrentTime(), operation, detail, LOG_SUCCESS, "");
    }
}
