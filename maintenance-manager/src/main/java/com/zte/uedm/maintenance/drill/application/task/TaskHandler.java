package com.zte.uedm.maintenance.drill.application.task;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.CancellationException;
import java.util.stream.Collectors;

import com.google.common.collect.Iterables;
import org.apache.commons.collections.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.zte.uedm.common.configuration.resource.bean.ResourceBaseBean;
import com.zte.uedm.maintenance.drill.application.constant.DrillConstant;
import com.zte.uedm.maintenance.drill.application.utils.DataConvertUtil;
import com.zte.uedm.maintenance.drill.application.utils.SpringContextAware;
import com.zte.uedm.maintenance.drill.domain.service.DrillRecordService;
import com.zte.uedm.maintenance.drill.domain.service.DrillTemplateService;
import com.zte.uedm.maintenance.drill.infrastructure.repository.persistence.DrillRecordEventRepository;
import com.zte.uedm.maintenance.drill.infrastructure.repository.persistence.DrillTemplateRepository;
import com.zte.uedm.maintenance.drill.infrastructure.repository.pojo.DrillRecordEventPojo;
import com.zte.uedm.maintenance.drill.infrastructure.rpc.impl.DrillAlarmRpcImpl;
import com.zte.uedm.maintenance.drill.infrastructure.rpc.impl.DrillConfigurationRpcImpl;
import com.zte.uedm.maintenance.drill.interfaces.web.bean.task.DrillTaskBean;
import com.zte.uedm.maintenance.malfunction.infrastructure.client.alarm.bean.Alarm;
import com.zte.uedm.redis.service.RedisService;

import jersey.repackaged.com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/5/31
 **/
@Slf4j
class TaskHandler implements Callable<String> {

    /**
     * 获取告警 error msg
     */
    private static final String GET_ALARM_ERROR_CN = "获取告警失败;";

    private static final String GET_ALARM_ERROR_EN = "Query active alarm error;";

    private static final String ALARM_MSG_CN = "预期%s告警,实际%s告警;";

    private static final String ALARM_MSG_EN = "Expect %s alarm,actual %s alarm;";

    public final static String REDIS_KEY_STANDARD_DATA = "standard-data:";
    private static final String GET_STANDARD_VALUE_ERROR_CN = "获取标准测点值错误;";
    private static final String GET_STANDARD_VALUE_ERROR_EN = "Query standard value error;";
    private static final String LOWER_UPPER_MSG_CN = "预期范围%s-%s,实际值%s;";

    private static final String LOWER_UPPER_MSG_EN = "Expect range is %s-%s, actual value is %s;";

    @Getter
    private List<DrillTaskBean> taskList;

    private final String recordId;

    private final String templateId;

    private final String topoId;
    private final Instant startTime;

    private boolean taskFlag;

    DrillRecordService drillRecordService;

    DrillRecordEventRepository drillRecordEventRepository;

    RedisService redisService;

    DrillConfigurationRpcImpl drillConfigurationRpc;

    DrillAlarmRpcImpl drillAlarmRpc;

    DrillTemplateRepository drillTemplateRepository;

    DrillTemplateService drillTemplateService;

    public TaskHandler(List<DrillTaskBean> taskList, String recordId, String templateId, String topoId) {
        this.taskList = taskList;
        this.startTime = Instant.now();
        this.drillRecordService = SpringContextAware.getBean(DrillRecordService.class);
        this.drillRecordEventRepository = SpringContextAware.getBean(DrillRecordEventRepository.class);
        this.redisService = SpringContextAware.getBean(RedisService.class);
        this.drillConfigurationRpc = SpringContextAware.getBean(DrillConfigurationRpcImpl.class);
        this.drillAlarmRpc = SpringContextAware.getBean(DrillAlarmRpcImpl.class);
        this.recordId = recordId;
        this.templateId = templateId;
        this.drillTemplateRepository = SpringContextAware.getBean(DrillTemplateRepository.class);
        this.drillTemplateService = SpringContextAware.getBean(DrillTemplateService.class);
        this.topoId = topoId;
        this.taskFlag = false;
    }

    public Instant getStartTime() {
        return startTime;
    }

    public String getRecordId() {
        return recordId;
    }

    public String getTemplateId() {
        return templateId;
    }

    @Override
    public String call() throws Exception {
        log.info("start drill loop {}",System.currentTimeMillis());
        while (!taskFlag) {
            if (Thread.currentThread().isInterrupted()) {
                // 到期处理演练中的任务
                log.info("stop drill ,task list remain {}", taskList);
                throw new CancellationException("Task cancelled");
            }
            taskList = taskList.stream().filter(x -> null == x.getHandled() || !x.getHandled()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(taskList)) {
                // 如果任务列表已经全部执行完，跳出循环
                break;
            }
            for (DrillTaskBean task : taskList) {
                // 对任务进行处理并插入数据库
                // 实现任务处理逻辑和插入数据库逻辑
                doTask(task);
            }
            Thread.sleep(DrillConstant.DRILL_CYCLE * 1000);
        }
        log.info("drill task completed");
        return "Task completed";

    }

    public void handleExtraTasks() {
        // 处理到期还在演练中的任务
        // 将目标有告警的任务直接设置为演练失败
        taskList = taskList.stream().filter(x -> null == x.getHandled() || !x.getHandled()).collect(Collectors.toList());
        List<DrillTaskBean> targetPositiveList = taskList.stream()
                .filter(x -> DrillConstant.ContainsAlarm.HAS_ALARM == x.getActiveAlarm()).collect(Collectors.toList());
        List<TaskResultBean> resultBeansTempOne = getTaskResultBeanFromTask(targetPositiveList, true, false);
        // 将目标无告警的任务直接设置为演练成功
        List<DrillTaskBean> targetNegitiveList = taskList.stream()
                .filter(x -> DrillConstant.ContainsAlarm.NO_ALARM == x.getActiveAlarm()).collect(Collectors.toList());
        List<TaskResultBean> resultBeansTempTwo = getTaskResultBeanFromTask(targetNegitiveList, false, true);
        List<TaskResultBean> allResult = Lists.newArrayList(Iterables.concat(resultBeansTempOne, resultBeansTempTwo));
        List<DrillRecordEventPojo> allEventPojos = DataConvertUtil.convertTaskResults(allResult, recordId);
        if (CollectionUtils.isNotEmpty(allEventPojos)) {
            // 批量插入结果并发送更新数量事件
            drillRecordEventRepository.batchInsertRecordEvent(allEventPojos);
            UpdateCountEvent successUpdateCountEvent = getUpdateCountEvent(resultBeansTempTwo,true,recordId);
            UpdateCountEvent failUpdateCountEvent = getUpdateCountEvent(resultBeansTempOne, false, recordId);
            drillRecordService.handleEvent(successUpdateCountEvent);
            drillRecordService.handleEvent(failUpdateCountEvent);
        }
    }

    private UpdateCountEvent getUpdateCountEvent(List<TaskResultBean> resultBeansTempTwo, boolean result, String recordId) {
        if (CollectionUtils.isEmpty(resultBeansTempTwo)) {
            return null;
        }
        UpdateCountEvent updateCountEvent = new UpdateCountEvent();
        updateCountEvent.setRecordId(recordId);
        updateCountEvent.setCheckResult(result);
        updateCountEvent.setCount(resultBeansTempTwo.size());

        return updateCountEvent;
    }

    private List<TaskResultBean> getTaskResultBeanFromTask(List<DrillTaskBean> targetPositiveList, boolean target, boolean drillResult) {
        List<TaskResultBean> failList = targetPositiveList.stream().map(x -> {
            TaskResultBean taskResultBean = new TaskResultBean();
            taskResultBean.setResult(drillResult);
            taskResultBean.setMoId(x.getMoId());
            taskResultBean.setComponentId(x.getComponentId());
            ResourceBaseBean monitorObjectBean = drillConfigurationRpc.getMonitorObjectById(x.getMoId());
            String eventOrigin = null == monitorObjectBean ? x.getMoId() : monitorObjectBean.getName();
            taskResultBean.setEventOrigin(eventOrigin);
            taskResultBean.setHappenDate(new Date());
            taskResultBean.setEventDesc(getDesc(0, target));
            return taskResultBean;
        }).collect(Collectors.toList());
        return failList;
    }

    private void doTask(DrillTaskBean task) {
        String moId = task.getMoId();
        int activeAlarm = task.getActiveAlarm();
        List<String> alarmCodes = task.getAlarmCodes();
        String resType = task.getResType();
        String componentId = task.getComponentId();
        // check
        TaskResultBean taskResultBean = checkAlarm(moId, activeAlarm, alarmCodes, resType);
        if (null != taskResultBean) {
            taskResultBean.setComponentId(componentId);
            // 演练成功或者失败，增加记录
            DrillRecordEventPojo drillRecordEventPojo = DataConvertUtil.convertTaskResult(taskResultBean, recordId);
            drillRecordEventRepository.insertRecordEvent(drillRecordEventPojo);
            UpdateCountEvent updateCountEvent = new UpdateCountEvent();
            updateCountEvent.setCheckResult(taskResultBean.isResult());
            updateCountEvent.setRecordId(recordId);
            drillRecordService.handleEvent(updateCountEvent);
            // 演练成功或者失败的任务作为已经结束的任务不参与下个周期的判断
            task.setHandled(true);
        }
    }

    private TaskResultBean checkAlarm(String moId, int activeAlarm, List<String> alarmCodes,String resType) {
        List<Alarm> alarms = Optional.ofNullable(drillAlarmRpc.getAlarms(moId, alarmCodes, resType))
                .orElse(Lists.newArrayList());
        TaskResultBean result = null;
        if (DrillConstant.ContainsAlarm.HAS_ALARM == activeAlarm) {
            // 目标有告警
            result = checkAlarmExist(alarms, moId);
        } else {
            // 目标无告警
            result = checkAlarmNotExist(alarms, moId);
        }

        return result;
    }

    /**
     * 判断目标无告警的情况
     *
     * @param alarms
     * @param moId
     * @return
     */
    private TaskResultBean checkAlarmNotExist(List<Alarm> alarms, String moId) {
        if (CollectionUtils.isEmpty(alarms)) {
            // 目标无告警，实际无告警
            return null;
        } else {
            // 目标无告警，实际有告警
            TaskResultBean taskResultBean = new TaskResultBean();
            taskResultBean.setResult(false);
            taskResultBean.setMoId(moId);
            ResourceBaseBean monitorObjectBean = drillConfigurationRpc.getMonitorObjectById(moId);
            String eventOrigin = null == monitorObjectBean ? moId : monitorObjectBean.getName();
            taskResultBean.setEventOrigin(eventOrigin);
            taskResultBean.setHappenDate(new Date());
            taskResultBean.setEventDesc(getDesc(alarms.size(), false));
            return taskResultBean;
        }
    }

    /**
     * 判断目标有告警的情况下
     *
     * @param alarms 告警列表
     * @param moId
     * @return
     */
    private TaskResultBean checkAlarmExist(List<Alarm> alarms, String moId) {
        if (CollectionUtils.isEmpty(alarms)) {
            // 目标有告警，实际无告警
            return null;
        } else {
            // 目标有告警，实际有告警
            TaskResultBean taskResultBean = new TaskResultBean();
            taskResultBean.setResult(true);
            taskResultBean.setMoId(moId);
            ResourceBaseBean monitorObjectBean = drillConfigurationRpc.getMonitorObjectById(moId);
            String eventOrigin = null == monitorObjectBean ? moId : monitorObjectBean.getName();
            taskResultBean.setEventOrigin(eventOrigin);
            taskResultBean.setHappenDate(new Date());
            taskResultBean.setEventDesc(getDesc(alarms.size(), true));
            return taskResultBean;
        }
    }

    private String getDesc(int size, boolean target) {

        StringBuffer stringBufferCN = new StringBuffer();
        StringBuffer stringBufferEN = new StringBuffer();
        String expectCn = target ? "有" : "无";
        String expectEn = target ? "some" : "no";
        stringBufferCN.append(String.format(ALARM_MSG_CN, expectCn, String.valueOf(size)));
        stringBufferEN.append(String.format(ALARM_MSG_EN, expectEn, String.valueOf(size)));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("zh_CN", stringBufferCN.toString());
        jsonObject.put("en_US", stringBufferEN.toString());
        // 显式地将 StringBuffer 对象置为 null，以便垃圾回收
        stringBufferCN.delete(0, stringBufferCN.length());
        stringBufferEN.delete(0, stringBufferEN.length());
        return jsonObject.toJSONString();
    }

    // @SuppressWarnings("unchecked")
    // private boolean checkSmpId(String moId, List<DrillTemplateMonitorObjectDetailDto> smpRules,
    // StringBuffer stringBufferCN, StringBuffer stringBufferEN) {
    // Set<String> smpIdSet = smpRules.stream().map(DrillTemplateMonitorObjectDetailDto::getSmpId)
    // .collect(Collectors.toSet());
    // boolean result = true;
    // try {
    // Map<String, Object> resultMap = redisService.getCacheMap(REDIS_KEY_STANDARD_DATA + moId, smpIdSet);
    // for (DrillTemplateMonitorObjectDetailDto smpRule : smpRules) {
    // int define = smpRule.getAlgorithmDefine();
    // Map<String, String> valueMap = (Map<String, String>) resultMap.get(smpRule.getSmpId());
    // String value = valueMap.get("value");
    // if (AlgorithmDefineEnum.LOWER_UPER.getCode() == define) {
    // Double upper = smpRule.getUpThreshold();
    // Double lower = smpRule.getLowThreshold();
    // if (StringUtils.isEmpty(value) || new BigDecimal(value).compareTo(new BigDecimal(lower)) < 0
    // || new BigDecimal(value).compareTo(new BigDecimal(upper)) > 0) {
    // result = false;
    // }
    // String cn = String.format(LOWER_UPPER_MSG_CN, lower, upper, value);
    // String en = String.format(LOWER_UPPER_MSG_EN, lower, upper, value);
    // stringBufferCN.append(cn);
    // stringBufferEN.append(en);
    // }
    // }
    // } catch (Exception e) {
    // log.error("get smp value error", e);
    // stringBufferCN.append(GET_STANDARD_VALUE_ERROR_CN);
    // stringBufferEN.append(GET_STANDARD_VALUE_ERROR_EN);
    // }
    // return result;
    //
    // }

}
