package com.zte.uedm.maintenance.workbench.domain.service;

import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.maintenance.workbench.interfaces.web.dto.NoticeDTO;
import com.zte.uedm.maintenance.workbench.interfaces.web.po.*;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface NoticeService {

    void addNotice(NoticeDTO noticeDto, String userName, String lang) throws UedmException;

    List<NoticePublishedPO> getPublishedNotices(String userName, String vagueTitle, String fromTime, String endTime, String lang);

    NoticePublishDetailPO getPublishDetailNotice(String id, String lang);

    int documentation(String id);

    List<NoticeDraftPO> getDraftNotices(String userName, String vagueTitle, String fromTime, String endTime, String lang);

    NoticeDraftDetailPO getDraftDetailNotice(String id, String lang);

    int modify(NoticeDTO noticeDTO, String userName, String lang);

    int deleteDraft(String id);

    List<NoticeDocumentPO> getDocumentNotices(String userName, String vagueTitle, String publishFromTime, String publishEndTime, String documentFromTime, String documentEndTime, String lang);

    NoticeDocumentDetailPO getDocumentDetailNotice(String id, String lang);

    List<NoticePublishedForUserPO> getPublishedForUser(String userName, String vague, String fromTime, String endTime, String publishName, String lang);

    void exportPublishedForUser(String userName, String vague, String fromTime, String endTime, String publishName, String lang) throws IOException;
}
