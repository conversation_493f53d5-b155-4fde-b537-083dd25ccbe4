package com.zte.uedm.maintenance.malfunction.application.query;

import com.zte.uedm.maintenance.common.Constant;
import com.zte.uedm.maintenance.malfunction.domain.shared.enums.StatusEnums;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.LoginHelper;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Getter
@Setter
@ToString
public class MalfunctionListQuery {

    /**
     * 是否有全部查询权限true是有，false是没有
     */
    private Boolean allPermission;

    private String malfunctionName;

    /**
     * 故障级别（0"极低",1"低",2"中",3"高",4"极高"）
     */
    private List<Integer> malfunctionLevel;

    /**
     * 状态0:拟制;1:待处理;2:处理中;3:完结;4:取消
     */
    private List<Integer> status;

    /**
     * 故障单号
     */
    private String malfunctionCode;

    private String reporter;

    private String handler;

    private String creator;

    private String happenStartTime;

    private String happenEndTime;

    /**
     * 影响度0"低",1"中",2"高"
     */
    private List<Integer> effectLevel;

    /**
     * 紧急度(0"低",1"中",2"高")
     */
    private List<Integer> emergencyLevel;

    /**
     * 故障来源0"告警",1"巡检任务",2"维保任务",3"其他"
     */
    private List<Integer> malfunctionSource;

    /**
     * 动态表头排序的排序方式desc降序，asc升序
     */
    private String action;

    /**
     * code单号，名称name，状态status，当前处理人handler，级别flexible_int3，发生时间flexible_str5，影响度flexible_int1，紧急度flexible_int2，来源flexible_int4，
     *                   报账人flexible_str4， 创建人creator。
     */
    private String sort;

    /**
     * 当前用户
     */
    private String user;

    private Integer pageNo;

    private Integer pageSize;

    private List<String> workflowIds;

    //自己处理过的工单
    private List<String> selfDownWorkflowIds;

    //是否查询所有工单   如果查询所有工单时，拥有查询所有权限allPermission时特殊处理
    private Boolean isSearchAll = false;

    /**
     * 当前处理人
     */
    private String handlerName;

    /**
     * 创建人
     */
    private String creatorName;

    private static final String STATUS_DEFINED = "status";

    public void setUser()
    {
        this.user = LoginHelper.getLoginUserName();
    }

    public void setCreatedStatus()
    {
        if (CollectionUtils.isEmpty(this.status))
        {
            Integer[] created = {StatusEnums.DRAFT.getCode(), StatusEnums.PENDING.getCode(), StatusEnums.PROCESSING.getCode(), StatusEnums.HANGUP.getCode(), StatusEnums.END.getCode(), StatusEnums.CANCEL.getCode()};
            this.status = new ArrayList<>(Arrays.asList(created));
        }
    }

    public void setPendingStatus()
    {
        if (CollectionUtils.isEmpty(this.status))
        {
            Integer[] Pending = {StatusEnums.PENDING.getCode(), StatusEnums.PROCESSING.getCode(), StatusEnums.HANGUP.getCode()};
            this.status = new ArrayList<>(Arrays.asList(Pending));
        }
    }

    public void setHandledStatus()
    {
        if (CollectionUtils.isEmpty(this.status))
        {
            Integer[] Handled = {StatusEnums.DRAFT.getCode(), StatusEnums.PENDING.getCode(), StatusEnums.PROCESSING.getCode(), StatusEnums.HANGUP.getCode(), StatusEnums.END.getCode(), StatusEnums.CANCEL.getCode()};
            this.status = new ArrayList<>(Arrays.asList(Handled));
        }
    }

    public void setAllStatus()
    {
        if (CollectionUtils.isEmpty(this.status))
        {
            Integer[] All = {StatusEnums.DRAFT.getCode(), StatusEnums.PENDING.getCode(), StatusEnums.PROCESSING.getCode(), StatusEnums.HANGUP.getCode(), StatusEnums.END.getCode(), StatusEnums.CANCEL.getCode()};
            this.status = new ArrayList<>(Arrays.asList(All));
        }
    }

    public void setSearchAll()
    {
        this.isSearchAll = true;
    }

    public String[] getSortElement()
    {
        if (!StringUtils.isBlank(this.sort)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "malfunctionCode");
            map.put("name", "malfunctionName");
            map.put(MalfunctionListQuery.STATUS_DEFINED, MalfunctionListQuery.STATUS_DEFINED);
            map.put("handler", "handler");
            map.put("flexible_int3", "malfunctionLevel");
            map.put("flexible_str5", "malfunctionHappenTime");
            map.put("flexible_int1", "effectLevel");
            map.put("flexible_int2", "emergencyLevel");
            map.put("flexible_int4", "reporter");
            map.put("creator", "creator");
            return new String[]{map.getOrDefault(this.sort, "name")};
        }
        else {
            return new String[]{MalfunctionListQuery.STATUS_DEFINED, "gmtCreate"};
        }

    }

    public Integer getProcessedAction()
    {
        if ("asc".equals(this.action))
        {
            return 1;
        } else {
            return Constant.FAILURE;
        }
    }
}
