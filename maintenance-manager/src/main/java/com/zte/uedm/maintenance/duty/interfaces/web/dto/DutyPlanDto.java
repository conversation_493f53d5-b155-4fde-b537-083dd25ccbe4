package com.zte.uedm.maintenance.duty.interfaces.web.dto;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class DutyPlanDto implements Serializable {

    @NotBlank
    private  String id;
    @NotBlank
    private  String planName;
    @NotBlank
    private  String orderInfoId;
    /*关联其他表的ID，通过此查找值班规则对应name*/
    @NotBlank
    private  String orderName;

    @NotBlank
    private  String groupId;
    /*关联其他表的ID，通过此查找值班组对应的人员userName*/
    @NotBlank
    private  String groupName;
    /*关联其他表的ID，通过此查找值班组对应的人员userName*/
    @NotBlank
    private  String[] groupMember;

    private  String[] planTemporaryMember;

    private  String planCreater;

    private  String planCreateTime;

    private  String planUpdater;

    private  String planUpdateTime;

    @NotBlank
    private  String planStartTime;
    @NotBlank
    private  String planEndTime;

    @NotNull
    @Pattern( regexp = "^([0-3])+$", message = "状态码存在4种，0未启用，1已启用，2已暂停，3已过期")
    private  int planStatus;

    private  String templateId;
    /*关联其他表的ID，查找值班模板对应的模板名称templateName*/

    private  String templateName;
    /*查找值班模板对应的名称*/

}
