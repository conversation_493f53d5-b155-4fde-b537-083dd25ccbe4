package com.zte.uedm.maintenance;

import com.zte.oes.dexcloud.commons.annotation.DexCloudApplication;
import com.zte.oes.dexcloud.commons.component.configclient.annotation.EnableConfigClient;
import com.zte.oes.dexcloud.commons.component.retrofit.annotation.EnableRetrofitRPC;
import com.zte.oes.dexcloud.i18n.api.annotation.EnableI18n;
import com.zte.oes.dexcloud.redis.redisson.annotation.EnableRedisson;
import com.zte.oes.dexcloud.uiframe.agent.impl.api.annotation.EnableUiframeagent;
import com.zte.uedm.maintenance.common.I18nService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.spring.boot.SecurityAutoConfiguration;
import org.dexcloud.springboot.kafka.config.annotation.EnableKafka;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.concurrent.TimeUnit;

@Slf4j
@SpringBootApplication(exclude = SecurityAutoConfiguration.class)
@DexCloudApplication
@EnableConfigClient
@EnableKafka
@EnableRedisson
@EnableRetrofitRPC
@EnableUiframeagent
@EnableI18n
@EnableAsync
@EnableScheduling
public class BootstrapApp
{
    public static void main(String[] args)
    {
        System.setProperty("Boot_Process_Duration", String.valueOf(TimeUnit.MINUTES.toSeconds(30)));
        SpringApplication.run(BootstrapApp.class, args);
    }

    @Bean
    public I18nService i18nService() {
        return new I18nService(messageSource());
    }

    @Bean
    public ResourceBundleMessageSource messageSource() {
        ResourceBundleMessageSource source = new ResourceBundleMessageSource();

        source.setBasename("i18n/maintenance");
        source.setUseCodeAsDefaultMessage(true);
        source.setDefaultEncoding("UTF-8");
        return source;
    }
}
