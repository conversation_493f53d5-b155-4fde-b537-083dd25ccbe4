/* Started by AICoder, pid:g5239x5d34x752d14f130a1420142052d1b5b3d0 */
package com.zte.uedm.maintenance.malfunction.interfaces.web.configuration;

import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.maintenance.malfunction.infrastructure.client.license.LicenseService;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.ResponseBeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@Path("license")
@Component
@Api(value = "license")
@Slf4j
public class LicenseController {

    @Autowired
    private LicenseService licenseService;

    /**
     * 查询是否有故障单license权限。
     *
     * @param languageOption 语言选项，通过HTTP头传递
     * @return 包含查询结果的ResponseBean对象
     */
    @GET
    @Path("/select-malfunction-init")
    @Consumes({ MediaType.APPLICATION_JSON })
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "查询是否有故障单license权限", notes = "查询是否有故障单license权限", httpMethod = "GET")
    public ResponseBean selectMalfunctionInit(@HeaderParam("language-option") String languageOption) {
        boolean result = licenseService.selectMalfunctionInit();
        return ResponseBeanUtils.getNormalResponseBean(0, result, 1);
    }

    /**
     * 转风险单鉴权。
     *
     * @param languageOption 语言选项，通过HTTP头传递
     * @return 包含鉴权结果的ResponseBean对象
     */
    @GET
    @Path("/select-risk-init")
    @Consumes({ MediaType.APPLICATION_JSON })
    @Produces({ MediaType.APPLICATION_JSON })
    @ApiOperation(value = "转风险单鉴权", notes = "转风险单鉴权", httpMethod = "GET")
    public ResponseBean selectRiskInit(@HeaderParam("language-option") String languageOption) {
        boolean result = licenseService.selectRiskInit();
        return ResponseBeanUtils.getNormalResponseBean(0, result, 1);
    }
}
/* Ended by AICoder, pid:g5239x5d34x752d14f130a1420142052d1b5b3d0 */