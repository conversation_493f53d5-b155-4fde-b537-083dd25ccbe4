package com.zte.uedm.maintenance.manager.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class MaintenanceItemResultDto {

    /**
     * 维保项id
     */
    private String id;
    /**
     * 维保模板id
     */
    private String maintenanceTemplateId;

    /**
     * 维保项名称
     */
    private String name;

    /**
     * 维保内容  2000字以内
     */
    private String content;

    /**
     * 维保项附件
     */
    private String itemAttachment;

    /**
     * 是否完成 true 已完成 false未完成
     */
    private Boolean complete;

    /**
     * 结论  1正常 2异常 3不涉及
     */
    private Integer conclusion;

    /**
     * 维保结果附件
     */
    private String resultAttachment;

    /**
     *异常内容
     */
    private String abnormalContent;

}
