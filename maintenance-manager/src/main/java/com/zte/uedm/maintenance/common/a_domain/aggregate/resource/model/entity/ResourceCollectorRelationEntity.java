package com.zte.uedm.maintenance.common.a_domain.aggregate.resource.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "关联采集器")
/* Started by AICoder, pid:9bb64e1004lbb69141f30af380a4992bb1b7d1db */
public class ResourceCollectorRelationEntity extends BaseEntity {
    @ApiModelProperty(value = "实例id")
    private String resourceId;

    @ApiModelProperty(value = "采集器id")
    private String collectorId;

    @ApiModelProperty(value = "采集器添加顺序")
    private String collectorSequence;

    @ApiModelProperty(value = "映射模板id")
    private String pointMappingTemplateId;

    @ApiModelProperty(value = "设备索引")
    private String deviceIndex;

    @ApiModelProperty(value = "测点索引")
    private String pointIndex;

    @ApiModelProperty(value = "模板激活顺序")
    private String templateSequence;

    @Override
    public String getPrimaryKey() {
        return this.getResourceId();
    }
}
/* Ended by AICoder, pid:9bb64e1004lbb69141f30af380a4992bb1b7d1db */