package com.zte.uedm.maintenance.workbench.domain.service;

import com.zte.uedm.maintenance.duty.interfaces.web.dto.ResponseDutyBean;
import com.zte.uedm.maintenance.workbench.interfaces.web.bean.FlowResponseBean;
import com.zte.uedm.maintenance.workbench.interfaces.web.bean.StatisticsFlowDataBean;
import com.zte.uedm.maintenance.workbench.interfaces.web.bean.WorkBenchControllerSaveTemplateRequestBean;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.maintenance.duty.interfaces.web.dto.DutyPlanCalendarAllDto;
import com.zte.uedm.maintenance.duty.interfaces.web.dto.DutyPlanCalendarDto;
import com.zte.uedm.maintenance.duty.interfaces.web.dto.DutyPlanLegendDto;
import com.zte.uedm.maintenance.workbench.interfaces.web.dto.MyProcessListVO;
import com.zte.uedm.maintenance.workbench.interfaces.web.dto.NoticeDTO;
import com.zte.uedm.maintenance.workbench.interfaces.web.dto.ProcessQueryVO;
import com.zte.uedm.maintenance.workbench.interfaces.web.vo.StatisticsFlowDataQueryVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface WorkBenchService {

    String demon();

    MyProcessListVO getMyProcessList(ProcessQueryVO processQuery,String user, String accessToken,Integer countDown,String languageOption) throws UedmException;

    void exportMyProcess(String languageOption,String user,String accessToken, HttpServletResponse response, ProcessQueryVO processQuery) ;

    ResponseDutyBean getTemplateByUser();

    ResponseDutyBean saveTemplate(WorkBenchControllerSaveTemplateRequestBean workBenchControllerSaveTemplateRequestBean);

    void addStatisticsFlowData(List<StatisticsFlowDataBean> statisticsFlowDataBeans);

    FlowResponseBean getStatisticsFlowData(StatisticsFlowDataQueryVo statisticsFlowDataQueryVo);

}
