package com.zte.uedm.maintenance.workbench.infrastructure.common.config;

import com.zte.uedm.maintenance.risk.application.executor.impl.RiskQueryServiceImpl;
import com.zte.uedm.maintenance.workbench.domain.service.impl.WorkBenchServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;

@Component()
@Slf4j
public class WorkbenchThreadPoolConfig {

    @Bean(name = "workbenchThreadPool")
    public ThreadPoolExecutor creatPool(){
        int core = Runtime.getRuntime().availableProcessors();
        long keepAliveTime = 10;
        BlockingDeque<Runnable> queue = new LinkedBlockingDeque<>(5);
        ThreadFactory factory = (Runnable runnable) -> generateThread( runnable);
        return new ThreadPoolExecutor(core+1, core+1, keepAliveTime, TimeUnit.SECONDS, queue,  factory,new ThreadPoolExecutor.AbortPolicy());
    }

    public Thread generateThread(Runnable runnable){
        Thread thread = new Thread(runnable);
        thread.setDefaultUncaughtExceptionHandler((Thread thread1, Throwable e) -> handleExceptionMethod(e));
        return thread;
    }

    public void handleExceptionMethod(Throwable exception){
        log.error("ThreadPoolExecutor error,msg is {}",exception.getMessage(),exception);
        CountDownLatch countDownLatch = WorkBenchServiceImpl.localCountDownLatch.get();
        if(countDownLatch != null){
            countDownLatch.countDown();
        }
    }

}
