package com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceresult.persistence;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.uedm.maintenance.manager.domain.aggregate.maintenanceresult.repository.MaintenanceResultRepository;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceresult.mapper.MaintenanceResultMapper;
import com.zte.uedm.maintenance.manager.infrastructure.repository.maintenanceresult.po.MaintenanceResultPO;
import com.zte.uedm.maintenance.manager.interfaces.web.vo.MaintenanceResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.BeanUtils;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 */

@Slf4j
@Repository
public class MaintenanceResultRepositoryImpl extends ServiceImpl<MaintenanceResultMapper, MaintenanceResultPO>
        implements MaintenanceResultRepository {

    @Resource
    private MaintenanceResultMapper maintenanceResultMapper;

    @Override
    public List<MaintenanceResultPO> getMaintenanceResultByTaskIdAndStatus(String taskId, Integer status) {
        return maintenanceResultMapper.selectList(new QueryWrapper<MaintenanceResultPO>()
                .eq("maintenance_task_id", taskId)
                .eq("status", status));
    }
    @Transactional
    @Override
    public void temporaryOrComplete(List<MaintenanceResultVO> resultVOS) {
        //对之前的数据进行删除，然后对新的数据进行保存
        String maintenanceTaskId=resultVOS.get(0).getMaintenanceTaskId();
        QueryWrapper<MaintenanceResultPO> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("maintenance_task_id",maintenanceTaskId);
        maintenanceResultMapper.delete(queryWrapper);
        maintenanceResultMapper.insertList(convertVOTOPO(resultVOS));
    }

    private List<MaintenanceResultPO> convertVOTOPO(List<MaintenanceResultVO> resultVOS){
        List<MaintenanceResultPO> resultPOS=new ArrayList<>();
        resultVOS.forEach(item->{
            MaintenanceResultPO resultPO=new MaintenanceResultPO();
            BeanUtils.copyProperties(item,resultPO);
            resultPOS.add(resultPO);
        });
        return resultPOS;
    }
}
