package com.zte.uedm.maintenance.common.a_domain.aggregate.resource.model.entity;

import com.zte.uedm.component.caffeine.bean.BaseCacheBean;
import com.zte.uedm.maintenance.common.a_domain.utils.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
/* Started by AICoder, pid:c2b19141fea46971431308da705c853b0f28b006 */
public class BaseEntity extends BaseCacheBean {
    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "更新时间")
    private Date gmtModified;

    @ApiModelProperty(value = "更新人")
    private String updater;

    public void supplementCreatorInfo(String creator, Date gmtCreate) {
        this.creator = creator;
        this.gmtCreate = gmtCreate;
    }

    public void supplementModifiedInfo(String updater, Date gmtModified) {
        this.updater = updater;
        this.gmtModified = gmtModified;
    }
    public String getGmtCreateToString(){
        if(gmtCreate==null){
            return "";
        }
     return DateUtils.getStrDate(gmtCreate);
    }
    public String getGmtModifiedToString(){
        if(gmtModified==null){
            return "";
        }
        return DateUtils.getStrDate(gmtModified);
    }
}

/* Ended by AICoder, pid:c2b19141fea46971431308da705c853b0f28b006 */