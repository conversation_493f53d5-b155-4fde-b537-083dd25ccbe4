package com.zte.uedm.maintenance.malfunction.infrastructure.client.alarm;

import com.zte.uedm.maintenance.malfunction.infrastructure.client.alarm.bean.Alarm;
import com.zte.uedm.maintenance.malfunction.infrastructure.client.alarm.bean.AlarmCode;

import java.util.List;

public interface AlarmService {

    /**
     * 获取实时告警
     * @param list
     * @param lang
     * @return
     */
    List<Alarm> getActiveAlarm(List<AlarmCode> list, String lang);

    /**
     * 获取历史告警
     * @param alarmKey
     * @param lang
     * @return
     */
    List<Alarm> getHistoryAlarm(String alarmKey, String lang);
}
