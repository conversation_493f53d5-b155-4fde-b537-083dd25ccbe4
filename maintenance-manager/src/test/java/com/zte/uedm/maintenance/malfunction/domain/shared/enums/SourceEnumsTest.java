package com.zte.uedm.maintenance.malfunction.domain.shared.enums;

import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version 1.0
 */
public class SourceEnumsTest {

    @Test
    public void sourceEnumsTest(){
        Integer low=SourceEnums.ALARM.getCode();
        Assert.assertEquals(low,Integer.valueOf(0));
        String cnString=SourceEnums.getCnStringByCode(0);
        Assert.assertEquals("告警",cnString);
        String enString=SourceEnums.getEnStringByCode(0);
        Assert.assertEquals("Alarm",enString);
    }
}
