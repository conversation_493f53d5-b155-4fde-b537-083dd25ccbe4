package com.zte.uedm.maintenance.duty.interfaces.web;

import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.maintenance.duty.domain.service.DutyOverviewService;
import com.zte.uedm.maintenance.duty.interfaces.web.dto.DutyMemberDto;
import com.zte.uedm.maintenance.duty.interfaces.web.dto.DutyOverviewCalendarDto;
import com.zte.uedm.maintenance.duty.interfaces.web.dto.DutyOverviewDto;
import com.zte.uedm.maintenance.duty.interfaces.web.dto.ResponseDutyBean;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;


public class DutyOverviewControllerTest {

    @InjectMocks
    private DutyOverviewController dutyOverviewController;

    @Mock
    DutyOverviewService dutyOverviewService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }
    @Test
    public void getHandoverTaskByName() throws UedmException {
        List<DutyOverviewDto> dutyOverviewDtoList = new ArrayList<>();
        Mockito.when(dutyOverviewService.getHandoverTaskByName(1,10,"id","asc")).thenReturn(dutyOverviewDtoList);
        Mockito.when(dutyOverviewService.countHandoverTask()).thenReturn(2);
        dutyOverviewController.getHandoverTaskByName(1,10,"id","asc");
        Mockito.doThrow(UedmException.class).when(dutyOverviewService).getHandoverTaskByName(Mockito.anyInt(),Mockito.anyInt(),Mockito.anyString(),Mockito.anyString());
        ResponseDutyBean responseDutyBean = dutyOverviewController.getHandoverTaskByName(Mockito.anyInt(),Mockito.anyInt(),Mockito.anyString(),Mockito.anyString());
        Assert.assertEquals(responseDutyBean.getMessage(),null);
    }
    @Test
    public void getHandoverTaskinfoByName() throws UedmException {
        List<DutyOverviewDto> dutyOverviewDtoList = new ArrayList<>();
        Mockito.when(dutyOverviewService.getHandoverTaskinfoByName("id","asc")).thenReturn(dutyOverviewDtoList);
        Mockito.when(dutyOverviewService.countHandoverTask()).thenReturn(2);
        dutyOverviewController.getHandoverTaskinfoByName("id","asc");
        Mockito.doThrow(UedmException.class).when(dutyOverviewService).getHandoverTaskinfoByName(Mockito.anyString(),Mockito.anyString());
        ResponseDutyBean responseDutyBean = dutyOverviewController.getHandoverTaskinfoByName(Mockito.anyString(),Mockito.anyString());
        Assert.assertEquals(responseDutyBean.getMessage(),null);
    }

    @Test
    public void getOverviewCalendarAll() throws UedmException {
        List<DutyOverviewCalendarDto> dutyOverviewCalendarList  = new ArrayList<>();
        Mockito.when(dutyOverviewService.getOverviewCalendarAll()).thenReturn(dutyOverviewCalendarList);
        dutyOverviewController.getOverviewCalendarAll();
        Mockito.doThrow(UedmException.class).when(dutyOverviewService).getOverviewCalendarAll();
        ResponseDutyBean responseDutyBean = dutyOverviewController.getOverviewCalendarAll();
        Assert.assertEquals(responseDutyBean.getMessage(),null);
    }

    @Test
    public void getDutyMemberTodayNow() throws UedmException {
        List<DutyMemberDto> dutyMemberDtoList = new ArrayList<>();
        Mockito.when(dutyOverviewService.getDutyMemberTodayNow(1, 10)).thenReturn(dutyMemberDtoList);
        Mockito.when(dutyOverviewService.countDutyMemberTodayNow()).thenReturn(10);
        dutyOverviewController.getDutyMemberTodayNow(1, 10);
        Mockito.doThrow(UedmException.class).when(dutyOverviewService).getDutyMemberTodayNow(1, 10);
        ResponseDutyBean responseDutyBean = dutyOverviewController.getDutyMemberTodayNow(1, 10);
        Assert.assertEquals(responseDutyBean.getMessage(), null);
    }
}