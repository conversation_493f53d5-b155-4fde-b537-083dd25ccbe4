package com.zte.uedm.maintenance.manager.infrastructure.common.util;

import org.assertj.core.util.Lists;
import org.junit.Test;
import org.mockito.InjectMocks;

import java.util.List;

import static org.junit.Assert.assertEquals;

public class PageUtilTest {
    @InjectMocks
    private PageUtil PageUtil;

    @Test
    public void getPageList1()
    {
        List list = PageUtil.getPageList(null, 1, 10);
        assertEquals(0, list.size());
    }

    @Test
    public void getPageList2()
    {
        List<String> list = PageUtil.getPageList(Lists.newArrayList(new String()), null, null);
        assertEquals(1, list.size());
    }

    @Test
    public void getPageList3()
    {
        List<String> list = PageUtil.getPageList(
                Lists.newArrayList(new String(), new String(), new String()), 1, 2);
        assertEquals(2, list.size());
    }

    @Test
    public void getPageList4()
    {
        List<String> list = PageUtil.getPageList(
                Lists.newArrayList(new String(), new String(), new String()), 2, 2);
        assertEquals(1, list.size());
    }
}
