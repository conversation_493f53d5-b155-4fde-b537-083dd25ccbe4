package com.zte.uedm.maintenance.workbench.interfaces.web.bean;

import com.zte.uedm.maintenance.manager.infrastructure.common.util.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

public class FlowStatusBeanTest {

    @Test
    public void test() throws Exception {
        FlowStatusBean flowStatusBean = new FlowStatusBean();
        PojoTestUtil.TestForPojo(flowStatusBean.getClass());
        Map<String, String> map = new HashMap<>();
        flowStatusBean.setPending(map);
        Assert.assertNotNull(flowStatusBean.toString());
        Assert.assertEquals(map,  flowStatusBean.getPending());
    }
}
