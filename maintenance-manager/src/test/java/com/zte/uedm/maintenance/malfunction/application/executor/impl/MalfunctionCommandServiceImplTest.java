package com.zte.uedm.maintenance.malfunction.application.executor.impl;

import com.zte.oes.dexcloud.configcenter.configclient.service.ConfigService;
import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.DateTimeService;
import com.zte.uedm.maintenance.common.I18nService;
import com.zte.uedm.maintenance.malfunction.application.command.MalfunctionCommand;
import com.zte.uedm.maintenance.malfunction.application.command.MalfunctionFlowCommand;
import com.zte.uedm.maintenance.malfunction.application.command.MalfunctionStatusCommand;
import com.zte.uedm.maintenance.malfunction.domain.aggregate.malfunction.model.Malfunction;
import com.zte.uedm.maintenance.malfunction.domain.aggregate.malfunction.repository.MalfunctionRepository;
import com.zte.uedm.maintenance.malfunction.infrastructure.common.util.LogUtils;
import com.zte.uedm.maintenance.manager.infrastructure.client.user.impl.UserServiceRpcImpl;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.LoginHelper;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.SendEmailUtil;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.SendSmsUtil;
import com.zte.uedm.maintenance.workflow.activiti.bean.HistoryOperationBean;
import com.zte.uedm.maintenance.workflow.activiti.service.MalfunctionActivitiService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;


import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({LoginHelper.class})
public class MalfunctionCommandServiceImplTest {

    @InjectMocks
    private MalfunctionCommandServiceImpl malfunctionCommandService;

    @Mock
    private MalfunctionRepository malfunctionRepository;

    @Mock
    private DateTimeService dateTimeService;

    @Mock
    private MalfunctionActivitiService activitiService;

    @Mock
    private LogUtils logUtils;

    @Mock
    private SendEmailUtil sendEmailUtil;

    @Mock
    private SendSmsUtil sendSmsUtil;

    @Mock
    private I18nService i18nService;

    @Mock
    private ConfigService configService;

    @Mock
    private UserServiceRpcImpl userServiceRpc;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(LoginHelper.class);
        when(LoginHelper.getLoginUserName()).thenReturn("admin");
    }
    @Test
    public void deleteByIdTest(){
        Mockito.doNothing().when(logUtils).sendMalfunctionDeleteLog(Mockito.any());
        try {
            malfunctionCommandService.deleteById("aaa","aaa");
        }catch (UedmException e){
            Assert.assertEquals(e.getErrorId(),Integer.valueOf(-1));
        }
        try {
            malfunctionCommandService.deleteById("aaa","CN");
        }catch (UedmException e){
            Assert.assertEquals(e.getErrorId(),Integer.valueOf(-1));
        }
        Malfunction po=new Malfunction();
        po.setStatus(2);
        Mockito.doReturn(po).when(malfunctionRepository).findById(Mockito.any());
        try {
            malfunctionCommandService.deleteById("aaa","aaa");
        }catch (UedmException e){
            Assert.assertEquals(e.getErrorId(),Integer.valueOf(-2));
        }
        try {
            malfunctionCommandService.deleteById("aaa","CN");
        }catch (UedmException e){
            Assert.assertEquals(e.getErrorId(),Integer.valueOf(-2));
        }
        po.setStatus(0);
        Exception exception=null;
        try {
            malfunctionCommandService.deleteById("aaa","CN");
        }catch (Exception e){
            exception=e;
        }
        Assert.assertNull(exception);
    }

    @Test
    public void addTest() throws UedmException {
        Mockito.doReturn("2022-06-06 00:00:00").when(dateTimeService).getCurrentTime();
        MalfunctionCommand command=new MalfunctionCommand();
        command.setAttachment("aaa");
        command.setId("aaa");
        command.setDescribe("xxx");
        try {
            Mockito.doReturn("aaa").when(i18nService).getMessage(Mockito.any());
            malfunctionCommandService.add(command);
        }catch (UedmException e){
            Assert.assertEquals(Integer.valueOf(-20),e.getErrorId());
        }
        Malfunction malfunction=new Malfunction();
        malfunction.setStatus(3);
        Mockito.doReturn(malfunction).when(malfunctionRepository).findById(Mockito.any());
        try {
            Mockito.doReturn("aaa").when(i18nService).getMessage(Mockito.any());
            malfunctionCommandService.add(command);
        }catch (UedmException e){
            Assert.assertEquals(Integer.valueOf(-30),e.getErrorId());
        }
        malfunction.setStatus(0);
        Mockito.doReturn(false).when(malfunctionRepository).checkName(Mockito.any(),Mockito.any());
        try {
            Mockito.doReturn("aaa").when(i18nService).getMessage(Mockito.any());
            malfunctionCommandService.add(command);
        }catch (UedmException e){
            Assert.assertEquals(Integer.valueOf(-10),e.getErrorId());
        }
    }
    @Test
    public void add2Test() throws UedmException {
        Mockito.doNothing().when(logUtils).sendMalfunctionAddLog(Mockito.any());
        Mockito.doReturn("2022-06-06 00:00:00").when(dateTimeService).getCurrentTime();
        MalfunctionCommand command=new MalfunctionCommand();
        command.setAttachment("aaa");
        command.setDescribe("xxx");
        Malfunction malfunction=new Malfunction();
        malfunction.setStatus(0);
        Mockito.doReturn(malfunction).when(malfunctionRepository).findById(Mockito.any());
        malfunction.setStatus(0);
        Mockito.doReturn(true).when(malfunctionRepository).checkName(Mockito.any(),Mockito.any());
        Mockito.doReturn("aaa").when(i18nService).getMessage(Mockito.any());
        Boolean flag=   malfunctionCommandService.add(command);
        Assert.assertTrue(flag);
        malfunction.setWorkflowId("aaa");
        Mockito.doReturn("aaa").when(i18nService).getMessage(Mockito.any());
        Boolean flag2=   malfunctionCommandService.add(command);
        Assert.assertTrue(flag2);
    }

    @Test
    public void temporaryTest() throws UedmException {
        Mockito.doReturn("2022-06-06 00:00:00").when(dateTimeService).getCurrentTime();
        MalfunctionCommand command=new MalfunctionCommand();
        command.setAttachment("aaa");
        command.setId("aaa");
        command.setDescribe("xxx");
        try {
            Mockito.doReturn("aaa").when(i18nService).getMessage(Mockito.any());
            malfunctionCommandService.temporary(command);
        }catch (UedmException e){
            Assert.assertEquals(Integer.valueOf(-20),e.getErrorId());
        }
        Malfunction malfunction=new Malfunction();
        malfunction.setStatus(3);
        Mockito.doReturn(malfunction).when(malfunctionRepository).findById(Mockito.any());
        try {
            Mockito.doReturn("aaa").when(i18nService).getMessage(Mockito.any());
            malfunctionCommandService.temporary(command);
        }catch (UedmException e){
            Assert.assertEquals(Integer.valueOf(-30),e.getErrorId());
        }
        malfunction.setStatus(0);
        Mockito.doReturn(false).when(malfunctionRepository).checkName(Mockito.any(),Mockito.any());
        try {
            Mockito.doReturn("aaa").when(i18nService).getMessage(Mockito.any());
            malfunctionCommandService.temporary(command);
        }catch (UedmException e){
            Assert.assertEquals(Integer.valueOf(-10),e.getErrorId());
        }
    }

    @Test
    public void temporary2Test() throws UedmException {
        Mockito.doNothing().when(logUtils).sendMalfunctionTemporaryLog(Mockito.any());
        Mockito.doReturn("2022-06-06 00:00:00").when(dateTimeService).getCurrentTime();
        MalfunctionCommand command=new MalfunctionCommand();
        command.setAttachment("aaa");
        command.setDescribe("xxx");
        Malfunction malfunction=new Malfunction();
        malfunction.setStatus(0);
        Mockito.doReturn(malfunction).when(malfunctionRepository).findById(Mockito.any());
        malfunction.setStatus(0);
        Mockito.doReturn(true).when(malfunctionRepository).checkName(Mockito.any(),Mockito.any());
        Mockito.doReturn("aaa").when(i18nService).getMessage(Mockito.any());
        Boolean flag=   malfunctionCommandService.temporary(command);
        Assert.assertTrue(flag);
        malfunction.setWorkflowId("aaa");
        Mockito.doReturn("aaa").when(i18nService).getMessage(Mockito.any());
        Boolean flag2=   malfunctionCommandService.temporary(command);
        Assert.assertTrue(flag2);
    }

    @Test
    public void temporary3Test() throws UedmException {
        Mockito.doNothing().when(logUtils).sendMalfunctionTemporaryLog(Mockito.any());
        Mockito.doReturn("2022-06-06 00:00:00").when(dateTimeService).getCurrentTime();
        MalfunctionCommand command=new MalfunctionCommand();
        command.setAttachment("aaa");
        command.setDescribe("xxx");
        command.setWorkflowId("aaa");
        Malfunction malfunction=new Malfunction();
        malfunction.setStatus(0);
        Mockito.doReturn(malfunction).when(malfunctionRepository).findById(Mockito.any());
        malfunction.setStatus(0);
        Mockito.doReturn(true).when(malfunctionRepository).checkName(Mockito.any(),Mockito.any());
        Mockito.doReturn("aaa").when(i18nService).getMessage(Mockito.any());
        Boolean flag=   malfunctionCommandService.temporary(command);
        Assert.assertTrue(flag);
        malfunction.setWorkflowId("aaa");
        Mockito.doReturn("aaa").when(i18nService).getMessage(Mockito.any());
        Boolean flag2=   malfunctionCommandService.temporary(command);
        Assert.assertTrue(flag2);
    }

    @Test
    public void changeStatusTest() throws UedmException {
        MalfunctionStatusCommand command=new MalfunctionStatusCommand();
        command.setWorkflowId("aaaa");
        command.setOldStatus(0);
        command.setNewStatus(1);
        try {
            malfunctionCommandService.changeStatus(command);
        }catch (UedmException e){
            Assert.assertEquals(Integer.valueOf(-1),e.getErrorId());
        }
        Malfunction malfunction=new Malfunction();
        malfunction.setStatus(2);
        Mockito.doReturn(malfunction).when(malfunctionRepository).findByWorkflowId(Mockito.any());
        Mockito.doNothing().when(malfunctionRepository).saveChangeStatus(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        try {
            malfunctionCommandService.changeStatus(command);
        }catch (UedmException e){
            Assert.assertEquals(Integer.valueOf(-2),e.getErrorId());
        }
        malfunction.setStatus(0);
        ResponseBean responseBean= malfunctionCommandService.changeStatus(command);
        Assert.assertEquals(responseBean.getCode(),Integer.valueOf(0));
    }

    @Test
    public void dealFlowTest() throws UedmException {
        MalfunctionFlowCommand command=new MalfunctionFlowCommand();
        command.setId("aaa");
        command.setWorkflowId("bbb");
        command.setDealType(2);
        command.setCurrentHandler("ccc");
        command.setHandler("ddd");
        Malfunction malfunction=new Malfunction();
        malfunction.setId("aaa");
        malfunction.setStatus(1);
        malfunction.setHandler("ccc");
        Mockito.doReturn(malfunction).when(malfunctionRepository).findById(Mockito.any());
        Mockito.doNothing().when(logUtils).sendDealFlowLog(Mockito.any(),Mockito.any());
        List<HistoryOperationBean> historyOperationBeans=new ArrayList<>();
        HistoryOperationBean historyOperationBean1=new HistoryOperationBean();
        historyOperationBean1.setOperationName("aaa");
        historyOperationBean1.setHandlerName("aaa");
        HistoryOperationBean historyOperationBean2=new HistoryOperationBean();
        historyOperationBean2.setOperationName("aaa");
        historyOperationBean2.setHandlerName("aaa");
        HistoryOperationBean historyOperationBean3=new HistoryOperationBean();
        historyOperationBean3.setOperationName("aaa");
        historyOperationBean3.setHandlerName("aaa");
        historyOperationBeans.add(historyOperationBean1);
        historyOperationBeans.add(historyOperationBean2);
        historyOperationBeans.add(historyOperationBean3);
        Mockito.doReturn(historyOperationBeans).when(activitiService).getTimelineByProcessInstanceId(Mockito.any());
        Mockito.doReturn(null).when(configService).getGlobalProperty(Mockito.any());
        ResponseBean responseBean= malfunctionCommandService.dealFlow(command);
        Assert.assertEquals(responseBean.getCode(),Integer.valueOf(0));
    }

    @Test
    public void reminderTest() throws UedmException {
        try {
            ResponseBean responseBean=malfunctionCommandService.reminder("aaa");
        }catch (UedmException e){
            Assert.assertEquals(e.getErrorId(),Integer.valueOf(-1));
        }
        Malfunction malfunction=new Malfunction();
        malfunction.setId("aaa");
        malfunction.setStatus(1);
        malfunction.setHandler("ccc");
        Mockito.doReturn(malfunction).when(malfunctionRepository).findById(Mockito.any());
        Mockito.doReturn(null).when(configService).getGlobalProperty(Mockito.any());
        ResponseBean responseBean1=malfunctionCommandService.reminder("aaa");
        Assert.assertEquals(responseBean1.getCode(),Integer.valueOf(0));
    }


    @Test
    public void addFromAlarmTest() throws UedmException {
        Mockito.doReturn("2022-06-06 00:00:00").when(dateTimeService).getCurrentTime();
        MalfunctionCommand command=new MalfunctionCommand();
        command.setAttachment("aaa");
        command.setId("aaa");
        command.setDescribe("xxx");
        Mockito.doReturn("aaa").when(i18nService).getMessage(Mockito.any());
        malfunctionCommandService.addFromAlarm(command);

        Mockito.doThrow(new RuntimeException()).when(malfunctionRepository).store(Mockito.any());
        Mockito.doReturn("aaa").when(i18nService).getMessage(Mockito.any());
        Boolean flag = malfunctionCommandService.addFromAlarm(command);
        Assert.assertFalse(flag);
    }
}
