package com.zte.uedm.maintenance.malfunction.infrastructure.commom.util;

import com.zte.uedm.common.bean.ResponseBean;
import com.zte.uedm.common.enums.ParameterExceptionEnum;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.maintenance.malfunction.infrastructure.common.util.ResponseUtils;
import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class ResponseUtilsTest
{
    @Test
    public void testGetParameterBlankResponseBean()
    {
        ResponseBean responseBean = ResponseUtils.getParameterBlankResponseBean();
        assertEquals(-100, responseBean.getCode().longValue());
    }

    @Test
    public void testGetNormalResponseBean()
    {
        ResponseBean responseBean = ResponseUtils.getNormalResponseBean(null, null, null);
        ResponseUtils.getRemoteResponseBean(0,"11");
        assertEquals(null, responseBean.getCode());
    }

    @Test
    public void getParameterErrorResponseBeanTest()
    {
        ResponseBean responseBean = ResponseUtils.getParameterErrorResponseBean();
        assertEquals(ParameterExceptionEnum.WRONGFUL.getCode(), responseBean.getCode());
        ResponseBean responseBean1= ResponseUtils.getParameterErrorResponseBean("zh_CN");
        assertEquals(ParameterExceptionEnum.WRONGFUL.getCode(), responseBean1.getCode());
        ResponseBean responseBean2= ResponseUtils.getParameterErrorResponseBean("zh_EN");
        assertEquals(ParameterExceptionEnum.WRONGFUL.getCode(), responseBean2.getCode());
    }

    @Test
    public void testGetRemoteResponseBean()
    {
        ResponseBean responseBean = ResponseUtils
                .getResponseBeanByUedmException(new UedmException(-200, "rtshrts"));
        assertEquals(-200, responseBean.getCode().intValue());
    }

    @Test
    public void getResponseBeanByUedmExceptionTest(){
        ResponseBean aaa = ResponseUtils.getResponseBeanByUedmException(new Exception("aaa"));
        Assert.assertEquals(aaa.getMessage(),"aaa");
    }

    @Test
    public void getNormalResponseBeanTest(){
        ResponseBean normalResponseBean = ResponseUtils.getNormalResponseBean(-1, "aaa", "aaa", 1);
        ResponseBean normalResponseBean1 = ResponseUtils.getNormalResponseBean(null, "aaa", "aaa", 1);
        ResponseBean normalResponseBean2 = ResponseUtils.getNormalResponseBean(-1, null, "aaa", 1);
        ResponseBean normalResponseBean3 = ResponseUtils.getNormalResponseBean(-1, "aaa", null, 1);
        ResponseBean normalResponseBean4 = ResponseUtils.getNormalResponseBean(-1, "aaa", 1, null);
        Assert.assertNotNull(normalResponseBean);
        Assert.assertNotNull(normalResponseBean1);
        Assert.assertNotNull(normalResponseBean2);
        Assert.assertNotNull(normalResponseBean3);
        Assert.assertNotNull(normalResponseBean4);
    }

    @Test
    public void getNormalResponseBeanTest1(){
        ResponseBean normalResponseBean = ResponseUtils.getNormalResponseBean(-1, "aaa", 1);
        Assert.assertNotNull(normalResponseBean);
    }
}