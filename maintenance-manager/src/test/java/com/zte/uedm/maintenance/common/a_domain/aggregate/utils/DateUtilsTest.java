package com.zte.uedm.maintenance.common.a_domain.aggregate.utils;

import com.zte.uedm.maintenance.common.a_domain.utils.DateUtils;
import com.zte.uedm.maintenance.common.a_infrastructure.common.DateTypeConst;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @FileName : DateUtilsTest.java
 * @FileDesc : TODO
 * @Version : 1.0
 * <AUTHOR> 何杰10253457
 */
/* Started by AICoder, pid:v196c035b0e21c414d46087a82cf4d762c90cb3d */
public class DateUtilsTest
{

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetMonthBegin()
    {
        String date = DateUtils.getMonthBegin("2020-06-06 00:00:00");
        Assert.assertEquals("2020-06-01 00:00:00", date);
    }

    @Test
    public void testGetMonthBegin_ParseException()
    {

        try
        {
            String date = DateUtils.getMonthBegin("2020");
            Assert.assertNull(date);
        }
        catch (Exception e)
        {
            // TODO: handle exception
        }

    }

    @Test
    public void testGetDayBegin()
    {
        String date = DateUtils.getDayBegin("2020-06-06 00:00:00");
        DateUtils.getMinTimeOfTwoDayBeforeOneDay(new Date());
        Assert.assertEquals("2020-06-06 00:00:00", date);
    }

    @Test
    public void testGetDayBegin_ParseException()
    {
        try
        {
            String date = DateUtils.getDayBegin("2020");
            Assert.assertNull(date);
        }
        catch (Exception e)
        {
            // TODO: handle exception
        }

    }

    @Test
    public void getMinTimeOfMonthTest()
    {
        Date date = new Date();
        Date date1 = DateUtils.getMinTimeOfMonth(date);
        Assert.assertNotNull(date1);
    }

    @Test
    public void getMaxTimeOfMonthTest()
    {
        Date date = new Date();
        Date date1 = DateUtils.getMaxTimeOfMonth(date);
        Assert.assertNotNull(date1);
    }

    @Test
    public void getMinTimeOfYearTest()
    {
        String date1 = null;
        try
        {
            Date date = new Date();
            date1 = DateUtils.getMinTimeOfYear(date);
            DateUtils.getMaxTimeOfDayBeforeOneDay(date);
            DateUtils.getMaxTimeOfDayBeforeTwoDay(date);
        }
        catch (ParseException e)
        {
            Assert.assertEquals(e.getClass(), ParseException.class);
        }
        Assert.assertNotNull(date1);
    }

    @Test
    public void getMaxTimeOfYearTest()
    {
        try
        {
            Date date = new Date();
            String date1 = DateUtils.getMaxTimeOfYear(date);
            Assert.assertNotNull(date1);
        }
        catch (ParseException e)
        {
            Assert.assertNotNull(e);
        }
    }

    @Test
    public void getMaxTimeStrOfMonthTest()
    {
        Date date = new Date();
        String date1 = DateUtils.getMaxTimeStrOfMonth(date);
        Assert.assertNotNull(date1);
    }

    @Test
    public void getMinTimeStrOfMonthTest()
    {
        Date date = new Date();
        String date1 = DateUtils.getMinTimeStrOfMonth(date);
        Assert.assertNotNull(date1);
    }

    @Test
    public void getMinTimeOfDayTest()
    {
        Date date = new Date();
        Date date1 = DateUtils.getMinTimeOfDay(date);
        Assert.assertNotNull(date1);
    }

    @Test
    public void getMaxTimeOfDayTest()
    {
        Date date = new Date();
        Date date1 = DateUtils.getMaxTimeOfDay(date);
        Assert.assertNotNull(date1);
    }

    @Test
    public void getMinTimeStrOfDayTest()
    {
        Date date = new Date();
        String date1 = DateUtils.getMinTimeStrOfDay(date);
        Assert.assertNotNull(date1);
    }

    @Test
    public void getMaxTimeStrOfDayTest()
    {
        Date date = new Date();
        String date1 = DateUtils.getMaxTimeStrOfDay(date);
        Assert.assertNotNull(date1);
    }

    @Test
    public void getStrDateTest()
    {
        DateUtils.getStrDate(null);
        Date date = new Date();
        String date1 = DateUtils.getStrDate(date);
        Assert.assertNotNull(date1);
    }

    @Test
    public void getMinTimeStrOfDayBeforeOneDayTest() throws Exception
    {
        String specifiedDay = "2020-10-26 00:00:00";
        Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(specifiedDay);


        String time = DateUtils.getMinTimeStrOfDayBeforeOneDay(date);

        Assert.assertNotNull(time);
    }

    @Test
    public void getYearMonDayDate()
    {
        try
        {
            DateUtils.getYearMonDayDate("2022-06-27 16:00:00");
            Date date = new Date();
            Date yearMonDayDate = DateUtils.getYearMonDayDate(date);
            Assert.assertEquals(10, yearMonDayDate.toString().length());
            DateUtils.getYearMonDayDate("20");
        }
        catch (Exception e)
        {
            Assert.assertEquals(e.getClass(), ParseException.class);
        }
    }

    @Test
    public void addDayTest()
    {
        Date date = DateUtils.getYearMonDayDate("2022-06-27 16:00:00");
        Date day = DateUtils.addDay(date, 1);
        Assert.assertEquals(DateUtils.getStrDate(day), "2022-06-28 00:00:00");
    }

    @Test
    public void getYearMonDayDateStrTest()
    {
        Date date = DateUtils.getYearMonDayDate("2022-06-27 16:00:00");
        String dateStr = DateUtils.getYearMonDayDateStr(date);
        Assert.assertEquals(10, dateStr.toString().length());
        Assert.assertEquals(dateStr, "2022-06-27");
    }

    @Test
    public void getYearMonDateStrTest()
    {
        Date date = DateUtils.getYearMonDayDate("2022-06-27 16:00:00");
        String dateStr = DateUtils.getYearMonDateStr(date);
        Assert.assertEquals(7, dateStr.toString().length());
        Assert.assertEquals(dateStr, "2022-06");
    }

    @Test
    public void getStringDateStr() throws ParseException
    {
        Date stringDateStr = DateUtils.getStringDateStr("2022-06-27 16:00:00");
        Assert.assertSame(true,stringDateStr != null);
    }

    @Test
    public void getStringStr() throws ParseException
    {
        Date stringDateStr = DateUtils.getDateFromStr("2022-06-27 16:00:00.123");
        Assert.assertSame(true,stringDateStr != null);
    }

    @Test
    public void getDateByAdd() throws ParseException
    {
        String dateByAdd = DateUtils.getDateByAdd(new Date(), 10);
        Assert.assertSame(true,dateByAdd != null);
    }

    @Test
    public void getDateByAddMillisecond() throws ParseException
    {
        String a = DateUtils.getDateByAddMillisecond(DateUtils.getDateFromStr("2019-12-14 12:00:00.123"), -10);
        Assert.assertEquals("2019-12-14 02:00:00.123", a);
    }

    @Test
    public void getDateByAddOfMonth() throws ParseException
    {
        String dateByAdd = DateUtils.getDateByAddOfMonth(new Date(), 3);
        Assert.assertSame(true,dateByAdd != null);
    }

    @Test
    public void getDateTest() {
        Date result = DateUtils.getDate("2022-07-01 10:10:22", DateTypeConst.DATE_FORMAT_3);
        String strDate = DateUtils.getStrDate(result);
        Assert.assertEquals(strDate, "2022-07-01 00:00:00");
        Date date = DateUtils.getDate("2020", "111");
        Assert.assertTrue(Objects.isNull(date));
    }

    @Test
    public void getStrDateByDateTest() {
        Date date = DateUtils.getDate("2022-07-15 10:15:00", DateTypeConst.DATE_FORMAT_1);
        String result = DateUtils.getStrDateByDateType(date, DateTypeConst.DATE_FORMAT_3);
        String dat = DateUtils.getStrDateByDateType(null, DateTypeConst.DATE_FORMAT_2);
        Assert.assertEquals(dat, "");
        Assert.assertEquals(result, "2022-07");
    }

    @Test
    public void getYesTerDay()
    {
        Date date = DateUtils.getDate("2022-07-15 10:15:00", DateTypeConst.DATE_FORMAT_1);
        Date yesTerDay = DateUtils.getYesTerDay(date);
        Assert.assertEquals(yesTerDay.toString(), "2022-07-14");
    }

    @Test
    public void getFormateTime()
    {
        Exception flag = null;
        try
        {
            String formateTime = DateUtils.getFormateTime(1657851000000L);
        } catch (Exception e) {
            flag = new Exception();
        }
        Assert.assertSame(true, flag == null);
    }

    @Test
    public void getSegmentTime()
    {
         DateUtils.getSegmentTime("2024-04-01", null, 5);
        List<Pair<String, String>> segmentTime = DateUtils.getSegmentTime("2024-04-01", "2024-04-16", 5);
        Assert.assertEquals(segmentTime.size(), 4);

    }
}



/* Ended by AICoder, pid:v196c035b0e21c414d46087a82cf4d762c90cb3d */