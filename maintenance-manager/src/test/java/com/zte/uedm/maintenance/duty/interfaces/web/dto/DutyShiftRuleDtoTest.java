package com.zte.uedm.maintenance.duty.interfaces.web.dto;

import com.zte.uedm.maintenance.duty.interfaces.web.dto.DutyShiftRuleDto;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;


public class DutyShiftRuleDtoTest {
    @Test
    public void dutyPlanShowDtoTest() throws Exception {
        DutyShiftRuleDto dutyShiftRuleDto = new DutyShiftRuleDto();
        PojoTestUtil.TestForPojo(dutyShiftRuleDto.getClass());
        dutyShiftRuleDto.setId("xxx");
        Assert.assertNotNull(dutyShiftRuleDto.toString());
        Assert.assertEquals("xxx", dutyShiftRuleDto.getId());
    }

}