package com.zte.uedm.maintenance.duty.interfaces.web.dto;

import com.zte.uedm.maintenance.manager.infrastructure.common.util.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;



public class GroupTemporaryDtoTest {
    @Test
    public void groupTemporaryDtoTest() throws Exception {
        GroupTemporaryDto groupTemporaryDto = new GroupTemporaryDto();
        groupTemporaryDto.setGroupId("xx");
        PojoTestUtil.TestForPojo(groupTemporaryDto.getClass());
        Assert.assertEquals(groupTemporaryDto.getGroupId(),"xx");
    }

}