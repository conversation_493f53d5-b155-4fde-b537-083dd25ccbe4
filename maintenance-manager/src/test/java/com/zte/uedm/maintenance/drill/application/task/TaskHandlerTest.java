package com.zte.uedm.maintenance.drill.application.task;

import com.zte.uedm.common.configuration.resource.bean.ResourceBaseBean;
import com.zte.uedm.maintenance.drill.application.utils.SpringContextAware;
import com.zte.uedm.maintenance.drill.domain.service.DrillRecordService;
import com.zte.uedm.maintenance.drill.domain.service.DrillTemplateService;
import com.zte.uedm.maintenance.drill.infrastructure.repository.persistence.DrillRecordEventRepository;
import com.zte.uedm.maintenance.drill.infrastructure.repository.persistence.DrillTemplateRepository;
import com.zte.uedm.maintenance.drill.infrastructure.rpc.impl.DrillAlarmRpcImpl;
import com.zte.uedm.maintenance.drill.infrastructure.rpc.impl.DrillConfigurationRpcImpl;
import com.zte.uedm.maintenance.drill.interfaces.web.bean.task.DrillTaskBean;
import com.zte.uedm.maintenance.malfunction.infrastructure.client.alarm.bean.Alarm;
import com.zte.uedm.redis.service.RedisService;
import org.apache.commons.compress.utils.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.powermock.api.mockito.PowerMockito.*;

/**
 * <AUTHOR>
 * @date 2023/6/12
 **/
@PrepareForTest({SpringContextAware.class,Thread.class})
@RunWith(PowerMockRunner.class)
public class TaskHandlerTest {

    @Before
    public void setUp(){
        MockitoAnnotations.initMocks(this);
        mockStatic(SpringContextAware.class);
        mockStatic(Thread.class);
        when(Thread.currentThread().isInterrupted()).thenReturn(false,true);

        when(SpringContextAware.getBean(DrillRecordService.class)).thenReturn(mock(DrillRecordService.class));
        when(SpringContextAware.getBean(DrillRecordEventRepository.class)).thenReturn(mock(DrillRecordEventRepository.class));
        when(SpringContextAware.getBean(RedisService.class)).thenReturn(mock(RedisService.class));
        when(SpringContextAware.getBean(DrillConfigurationRpcImpl.class)).thenReturn(mock(DrillConfigurationRpcImpl.class));
        when(SpringContextAware.getBean(DrillTemplateRepository.class)).thenReturn(mock(DrillTemplateRepository.class));

    }

    @Test
    public void testDoTask() throws Exception {
        List<DrillTaskBean> taskList = new CopyOnWriteArrayList<>();
        DrillTaskBean drillTaskBean = new DrillTaskBean();
        drillTaskBean.setMoId("1");
        drillTaskBean.setActiveAlarm(0);

        taskList.add(drillTaskBean);
        Thread thread = mock(Thread.class);
        DrillConfigurationRpcImpl drillConfigurationRpc = mock(DrillConfigurationRpcImpl.class);
        when(SpringContextAware.getBean(DrillConfigurationRpcImpl.class)).thenReturn(drillConfigurationRpc);
        DrillTemplateService drillTemplateService = mock(DrillTemplateService.class);

        when(SpringContextAware.getBean(DrillTemplateService.class)).thenReturn(mock(DrillTemplateService.class));
        DrillAlarmRpcImpl drillAlarmRpc = mock(DrillAlarmRpcImpl.class);
        when(SpringContextAware.getBean(DrillAlarmRpcImpl.class)).thenReturn(drillAlarmRpc);

        when(drillConfigurationRpc.getMonitorObjectById(any())).thenReturn(new ResourceBaseBean());
        when(drillTemplateService.getAlarmCodesFromTopo(any())).thenReturn(new ArrayList<>());
        Alarm alarm = new Alarm();
        when(drillAlarmRpc.getAlarms(any(), any(), any())).thenReturn(Collections.singletonList(alarm));
        TaskHandler taskHandler = new TaskHandler(taskList, "1", "@", "@2");
        String result = taskHandler.call();
        Assert.assertEquals("Task completed", result);
    }

}