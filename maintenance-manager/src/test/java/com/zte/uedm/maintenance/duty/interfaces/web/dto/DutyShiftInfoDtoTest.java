package com.zte.uedm.maintenance.duty.interfaces.web.dto;

import com.zte.uedm.maintenance.duty.interfaces.web.dto.DutyShiftInfoDto;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;


public class DutyShiftInfoDtoTest {

    @Test
    public void dutyShiftInfoDtoTest() throws Exception {
        DutyShiftInfoDto dutyShiftInfoDto = new DutyShiftInfoDto();
        dutyShiftInfoDto.setOrderTimeName("xxx");
        PojoTestUtil.TestForPojo(dutyShiftInfoDto.getClass());
        Assert.assertNotNull(dutyShiftInfoDto.toString());
        Assert.assertEquals("xxx", dutyShiftInfoDto.getOrderTimeName());
    }



}