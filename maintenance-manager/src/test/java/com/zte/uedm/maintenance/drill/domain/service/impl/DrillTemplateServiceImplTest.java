package com.zte.uedm.maintenance.drill.domain.service.impl;

import com.zte.uedm.common.configuration.idname.bean.IdNameBean;
import com.zte.uedm.common.configuration.point.bean.StandardExpandPointBean;
import com.zte.uedm.common.configuration.point.bean.StandardPointBean;
import com.zte.uedm.common.configuration.resource.bean.ResourceBaseBean;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.maintenance.drill.infrastructure.common.config.RedisLockConfig;
import com.zte.uedm.maintenance.drill.infrastructure.repository.mapper.DrillTemplateMapper;
import com.zte.uedm.maintenance.drill.infrastructure.repository.pojo.DrillTemplateMonitorObjectRelationPojo;
import com.zte.uedm.maintenance.drill.infrastructure.repository.pojo.DrillTemplatePojo;
import com.zte.uedm.maintenance.drill.interfaces.web.bean.topo.ElectricTopoElementAttributeBean;
import com.zte.uedm.maintenance.drill.interfaces.web.beans.DrillTemplateQueryBean;
import com.zte.uedm.maintenance.drill.interfaces.web.beans.MonitorObjectRuleBean;
import com.zte.uedm.maintenance.drill.interfaces.web.beans.topo.ElectricTopoShowBean;
import com.zte.uedm.maintenance.drill.interfaces.web.dto.DrillTemplateDto;
import com.zte.uedm.maintenance.drill.interfaces.web.dto.DrillTemplateItemDto;
import com.zte.uedm.maintenance.drill.interfaces.web.dto.DrillTemplateMonitorObjectDetailDto;
import com.zte.uedm.maintenance.drill.interfaces.web.dto.DrillTemplateQueryDto;
import com.zte.uedm.maintenance.drill.interfaces.web.enums.DrillStatusEnum;
import com.zte.uedm.maintenance.drill.interfaces.web.rpc.impl.ConfigurationRpcImpl;
import com.zte.uedm.maintenance.manager.infrastructure.common.util.I18nUtils;
import com.zte.uedm.maintenance.uft.FtMockitoAnnotations;
import com.zte.uedm.redis.bean.RedisValueStringBean;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

public class DrillTemplateServiceImplTest {

    @InjectMocks
    private DrillTemplateServiceImpl drillTemplateService;

    @Mock
    private DrillTemplateMapper drillTemplateMapper;

    @Mock
    private ConfigurationRpcImpl configurationRpcImpl;

    @Mock
    private JsonService jsonService;

    @Mock
    private I18nUtils i18nUtils;

    @Mock
    private RedisLockConfig redisLockConfig;

    @Before
    public void setUp() throws IllegalAccessException, ClassNotFoundException, InstantiationException {
        FtMockitoAnnotations.initMocks(this);
    }

    @Test
    public void testQueryDrillTemplateList() {
        List<DrillTemplateItemDto> dtoList = new ArrayList<>();
        drillTemplateService.queryDrillTemplateList(null, dtoList, "zh-CN");

        doReturn(null).when(drillTemplateMapper).selectDrillTemplateList(Mockito.any());
        drillTemplateService.queryDrillTemplateList(new DrillTemplateQueryDto(), dtoList, "zh-CN");

        DrillTemplateQueryDto queryDto = new DrillTemplateQueryDto();
        queryDto.setTemplateName("测试");
        queryDto.setPageSize(1);
        queryDto.setPageNo(1);
        DrillTemplateQueryBean queryBean = new DrillTemplateQueryBean(queryDto);
        List<DrillTemplatePojo> pojoList =  new ArrayList<>();
        DrillTemplatePojo drillTemplatePojo = new DrillTemplatePojo();
        drillTemplatePojo.setTemplateId("1234");
        drillTemplatePojo.setTemplateName("text");
        drillTemplatePojo.setCreatedTime(new Date());
        drillTemplatePojo.setUpdatedTime(new Date());
        drillTemplatePojo.setCreator("admin");
        drillTemplatePojo.setTopologyId("topo-id-12-01");
        pojoList.add(drillTemplatePojo);
        doReturn(pojoList).when(drillTemplateMapper).selectDrillTemplateList(Mockito.any());
        drillTemplateService.queryDrillTemplateList(queryDto, dtoList, "zh-CN");

        queryDto.setPageSize(1);
        queryDto.setPageNo(2);
        doReturn(pojoList).when(drillTemplateMapper).selectDrillTemplateList(Mockito.any());
        drillTemplateService.queryDrillTemplateList(queryDto, dtoList, "zh-CN");

        queryDto.setPageSize(null);
        queryDto.setPageNo(null);
        doReturn(pojoList).when(drillTemplateMapper).selectDrillTemplateList(Mockito.any());
        drillTemplateService.queryDrillTemplateList(queryDto, dtoList, "zh-CN");
        ElectricTopoShowBean showBean = new ElectricTopoShowBean();
        showBean.setName("ABC");
        showBean.setId("topo-id-12-01");
        List<ElectricTopoShowBean> topoShowBeans = new ArrayList<>();
        topoShowBeans.add(showBean);
        try {
            doReturn(topoShowBeans).when(configurationRpcImpl).getTopoListByIds(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString());
        } catch (UedmException ignored) {
        }

        queryDto.setPageSize(null);
        queryDto.setPageNo(null);
        doReturn(pojoList).when(drillTemplateMapper).selectDrillTemplateList(Mockito.any());
        drillTemplateService.queryDrillTemplateList(queryDto, dtoList, "zh-CN");

        try {
            doThrow(new UedmException(-1,"error")).when(configurationRpcImpl).getTopoListByIds(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString());
        } catch (UedmException ignored) {
        }
        queryDto.setPageSize(null);
        queryDto.setPageNo(null);
        doReturn(pojoList).when(drillTemplateMapper).selectDrillTemplateList(Mockito.any());
        Integer total = drillTemplateService.queryDrillTemplateList(queryDto, dtoList, "zh-CN");

        Assert.assertNotNull(total);

    }


    @Test
    public void testQueryDrillTemplateDetail() {

        drillTemplateService.queryDrillTemplateDetail("","zh-CN");

        doReturn(null).when(drillTemplateMapper).selectDrillTemplateById("1");
        drillTemplateService.queryDrillTemplateDetail("1","zh-CN");

        DrillTemplatePojo templatePojo = new DrillTemplatePojo();
        templatePojo.setTemplateId("1");
        doReturn(templatePojo).when(drillTemplateMapper).selectDrillTemplateById("1");
        ElectricTopoShowBean showBean = new ElectricTopoShowBean();
        showBean.setName("ABC");
        showBean.setId("topo-id-12-01");
        List<ElectricTopoShowBean> topoShowBeans = new ArrayList<>();
        topoShowBeans.add(showBean);
        List<ResourceBaseBean> resBeanList = new ArrayList<>();
        ResourceBaseBean resBean = new ResourceBaseBean();
        resBean.setId("id");
        resBean.setTemplateId("id");
        resBean.setRecordTemplateId("id");
        resBean.setParentId("id");
        resBean.setMoc("moc");
        resBean.setName("name");
        resBean.setDisplayname("d1");
        resBean.setDescription("dec");
        resBean.setNbiid("nbbid");
        resBean.setResourceType("rec");
        resBean.setGmtCreate("2020-01-01");
        resBean.setGmtModified("2020-01-01");
        resBean.setIdPath("path");
        resBean.setNamePath("path");
        resBean.setPathName("path");
        resBean.setRedisValueStringBean(new RedisValueStringBean());


        List<IdNameBean> idNameBeans = new ArrayList<>();
        IdNameBean idNameBean00 = new IdNameBean();
        idNameBean00.setId("0");
        idNameBean00.setName("AAAAAA");
        idNameBeans.add(idNameBean00);
        IdNameBean idNameBean01 = new IdNameBean();
        idNameBean01.setId("1");
        idNameBean01.setName("BBBBBB");
        idNameBeans.add(idNameBean01);

        resBeanList.add(resBean);
        try {
            doReturn(idNameBeans).when(configurationRpcImpl).getStandardPointOptionById(Mockito.any(), Mockito.anyString());
            doReturn(topoShowBeans).when(configurationRpcImpl).getTopoListByIds(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString());
            doReturn(resBeanList).when(configurationRpcImpl).getResourceByIdList(Mockito.any());
        } catch (UedmException ignored) {
        }
        List<DrillTemplateMonitorObjectRelationPojo> relationPojoList = new ArrayList<>();
        DrillTemplateMonitorObjectRelationPojo relationPojo = new DrillTemplateMonitorObjectRelationPojo();
        relationPojo.setTemplateId("111");
        relationPojo.setMonitorObjectId("111");
        relationPojo.setRule("{\"activeAlarm\":0,\"rule\":[{\"enumList\":[0],\"smpId\":\"dc.fire.alarm\",\"algorithmDefine\":1}]}");
        DrillTemplateMonitorObjectRelationPojo relationPojo01 = new DrillTemplateMonitorObjectRelationPojo();
        relationPojo01.setTemplateId("222");
        relationPojo01.setMonitorObjectId("id");
        relationPojo01.setRule("");
        relationPojoList.add(relationPojo);
        relationPojoList.add(relationPojo01);
        doReturn(relationPojoList).when(drillTemplateMapper).selectRelationMoList("1");
        DrillTemplateDto drdto = drillTemplateService.queryDrillTemplateDetail("1","zh-CN");


        try {
            doThrow(new UedmException(-1,"error")).when(configurationRpcImpl).getTopoListByIds(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString());
            doThrow(new UedmException(-1,"error")).when(configurationRpcImpl).getResourceByIdList(Mockito.any());
            doThrow(new UedmException(-1,"error")).when(configurationRpcImpl).getTopoTreeByName(Mockito.any(),Mockito.anyString());
        } catch (UedmException ignored) {
        }
         drillTemplateService.queryDrillTemplateDetail("1","zh-CN");

        try {
            doReturn(topoShowBeans).when(configurationRpcImpl).getTopoListByIds(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString());
            doReturn(resBeanList).when(configurationRpcImpl).getResourceByIdList(Mockito.any());
        } catch (UedmException ignored) {
        }
        drillTemplateService.queryDrillTemplateDetail("1","zh-CN");

        try {

            MonitorObjectRuleBean monitorObjectRuleBean = new MonitorObjectRuleBean();
            monitorObjectRuleBean.setActiveAlarm(1);
            List<DrillTemplateMonitorObjectDetailDto> rule = new ArrayList<>();
            DrillTemplateMonitorObjectDetailDto detailDto = new DrillTemplateMonitorObjectDetailDto();
            detailDto.setSmpId("dc.fire.alarm");
            detailDto.setSmpName("建筑消防告警");
            detailDto.setAlgorithmDefine(1);
            detailDto.setEnumList(Collections.singletonList(0));
            rule.add(detailDto);
            DrillTemplateMonitorObjectDetailDto detailDto02 = new DrillTemplateMonitorObjectDetailDto();
            detailDto02.setSmpId("acdc.outlet.temp");
            detailDto02.setSmpName("ACDC Outlet");
            detailDto02.setAlgorithmDefine(0);
            detailDto02.setUpThreshold(22.0);
            detailDto02.setLowThreshold(1.0);
            rule.add(detailDto02);
            monitorObjectRuleBean.setRule(rule);
            when(jsonService.jsonToObject(Mockito.any(),Mockito.any())).thenReturn(monitorObjectRuleBean);

            List<StandardPointBean> standardPointBeans = new ArrayList<>();
            StandardPointBean pointBean = new StandardPointBean();
            pointBean.setId("dc.fire.alarm");
            pointBean.setName("建筑消防告警");
            Map<Integer, Map<String,String>> embedMap = new HashMap<>();
            Map<String,String> innerMap00 = new HashMap<>();
            innerMap00.put("zh-CN","关闭");
            innerMap00.put("en-US","Closed");
            embedMap.put(0,innerMap00);
            pointBean.setNameI18n(innerMap00);
            Map<String,String> innerMap01 = new HashMap<>();
            innerMap01.put("zh-CN","开启");
            innerMap01.put("en-US","Open");
            embedMap.put(1,innerMap01);
            pointBean.setValueDefineI18n(embedMap);
            standardPointBeans.add(pointBean);
            StandardPointBean pointBean02 = new StandardPointBean();
            pointBean02.setId("acdc.outlet.temp");
            pointBean02.setName("ACDC Outlet");
            pointBean02.setUnit("rpm/s");
            pointBean02.setNameI18n(innerMap01);
            standardPointBeans.add(pointBean02);
            doReturn(standardPointBeans).when(configurationRpcImpl).getStandardPointByIdList(Arrays.asList("dc.fire.alarm", "acdc.outlet.temp"));

        } catch (UedmException ignored) {
        }
        drillTemplateService.queryDrillTemplateDetail("1","zh-CN");
        drillTemplateService.queryDrillTemplateDetail("1","fr-FR");

        try {
            doThrow(new UedmException(-1,"error")).when(configurationRpcImpl).getStandardPointByIdList(Arrays.asList("dc.fire.alarm", "acdc.outlet.temp"));
        } catch (UedmException ignored) {
        }
        drillTemplateService.queryDrillTemplateDetail("1","zh-CN");


        try {

            doReturn(new ArrayList<>()).when(configurationRpcImpl).getStandardPointByIdList(Arrays.asList("dc.fire.alarm", "acdc.outlet.temp"));

            MonitorObjectRuleBean monitorObjectRuleBean = new MonitorObjectRuleBean();
            monitorObjectRuleBean.setActiveAlarm(1);
            List<DrillTemplateMonitorObjectDetailDto> rule = new ArrayList<>();
            DrillTemplateMonitorObjectDetailDto detailDto = new DrillTemplateMonitorObjectDetailDto();
            detailDto.setSmpId("dc.fire.alarm.alp_v1");
            detailDto.setSmpName("建筑消防0告警");
            detailDto.setAlgorithmDefine(1);
            detailDto.setEnumList(Collections.singletonList(0));
            rule.add(detailDto);
            DrillTemplateMonitorObjectDetailDto detailDto02 = new DrillTemplateMonitorObjectDetailDto();
            detailDto02.setSmpId("dc.fire.alarm.alp_v2");
            detailDto02.setSmpName("建筑消防1告警");
            detailDto02.setAlgorithmDefine(0);
            detailDto02.setUpThreshold(22.0);
            detailDto02.setLowThreshold(1.0);
            rule.add(detailDto02);
            monitorObjectRuleBean.setRule(rule);
            when(jsonService.jsonToObject(Mockito.any(),Mockito.any())).thenReturn(monitorObjectRuleBean);

            List<StandardExpandPointBean> standardExpandPointBeans = new ArrayList<>();
            StandardExpandPointBean expandPointBean = new StandardExpandPointBean();
            expandPointBean.setId("dc.fire.alarm.alp_v1");
            expandPointBean.setName("建筑消防1告警");
            Map<Integer, Map<String,String>> embedMap = new HashMap<>();
            Map<String,String> innerMap00 = new HashMap<>();
            innerMap00.put("zh-CN","关闭");
            innerMap00.put("en-US","Closed");
            embedMap.put(0,innerMap00);
            expandPointBean.setNameI18n(innerMap00);
            Map<String,String> innerMap01 = new HashMap<>();
            innerMap01.put("zh-CN","开启");
            innerMap01.put("en-US","Open");
            embedMap.put(1,innerMap01);
            expandPointBean.setValueDefineI18n(embedMap);
            standardExpandPointBeans.add(expandPointBean);
            StandardExpandPointBean expandPointBean02 = new StandardExpandPointBean();
            expandPointBean02.setId("dc.fire.alarm.alp_v2");
            expandPointBean02.setName("建筑消防2告警");
            expandPointBean02.setUnit("rpm/s");
            expandPointBean02.setNameI18n(innerMap01);
            standardExpandPointBeans.add(expandPointBean02);
            doReturn(standardExpandPointBeans).when(configurationRpcImpl).getStandardExpandPointOptionByIdList(Arrays.asList("dc.fire.alarm.alp_v1", "dc.fire.alarm.alp_v2"),"zh-CN");

        } catch (UedmException ignored) {
        }
        drillTemplateService.queryDrillTemplateDetail("1","zh-CN");

        try {
            doThrow(new UedmException(-1,"error")).when(configurationRpcImpl).getStandardExpandPointOptionByIdList(Arrays.asList("dc.fire.alarm.alp_v1", "dc.fire.alarm.alp_v2"),"zh-CN");
        } catch (UedmException ignored) {
        }
        drillTemplateService.queryDrillTemplateDetail("1","zh-CN");

        try {
            when(jsonService.jsonToObject(Mockito.any(),Mockito.any())).thenThrow(new UedmException(-1,"aaa"));
        } catch (UedmException ignored) {
        }
        drillTemplateService.queryDrillTemplateDetail("1","zh-CN");



        Assert.assertNotNull(drdto);

    }

    @Test
    public void deleteDrillTemplate() {
        try {
            drillTemplateService.deleteMultiDrillTemplate(null,"zh-CN");
        } catch (UedmException ignored) {
        }

        doReturn(new ArrayList<>()).when(drillTemplateMapper).selectDrillTemplateByIdList(Mockito.any());
        try {
            drillTemplateService.deleteMultiDrillTemplate("1","zh-CN");
        } catch (UedmException ignored) {
        }

        List<DrillTemplatePojo> templatePojoList = new ArrayList<>();
        templatePojoList.add(new DrillTemplatePojo());
        try {
            doReturn(templatePojoList).when(drillTemplateMapper).selectDrillTemplateByIdList(Mockito.any());
            drillTemplateService.deleteMultiDrillTemplate("1","zh-CN");
        } catch (UedmException ignored) {
        }

        try{
            List<DrillTemplatePojo> pojoList01 = new ArrayList<>();
            DrillTemplatePojo drillPojo = new DrillTemplatePojo();
            drillPojo.setStatus(DrillStatusEnum.STARTED.getCode());
            pojoList01.add(drillPojo);
            doReturn(pojoList01).when(drillTemplateMapper).selectDrillTemplateByIdList(Mockito.any());
            drillTemplateService.deleteMultiDrillTemplate("1","zh-CN");
        }catch (UedmException ignored){
        }

        try{
            List<DrillTemplatePojo> pojoList02 = new ArrayList<>();
            DrillTemplatePojo drillPojo = new DrillTemplatePojo();
            drillPojo.setStatus(DrillStatusEnum.STARTED.getCode());
            pojoList02.add(drillPojo);
            pojoList02.add(new DrillTemplatePojo());
            doReturn(pojoList02).when(drillTemplateMapper).selectDrillTemplateByIdList(Mockito.any());
            drillTemplateService.deleteMultiDrillTemplate("1,2","zh-CN");
        }catch (UedmException ignored){
        }

        Assert.assertNotNull("drillTemplatePojo");

    }

    @Test
    public void test() {
        ElectricTopoElementAttributeBean electricTopoElementAttributeBean = new ElectricTopoElementAttributeBean();
        List<ElectricTopoElementAttributeBean> list = new ArrayList<>();
        list.add(electricTopoElementAttributeBean);
        List list1 = drillTemplateService.buildNoSmpIdDrillTopoBean(list);
        Assert.assertEquals(0, list1.size());
    }
}