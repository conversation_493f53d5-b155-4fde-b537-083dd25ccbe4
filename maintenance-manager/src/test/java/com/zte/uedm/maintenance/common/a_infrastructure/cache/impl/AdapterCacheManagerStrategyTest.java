package com.zte.uedm.maintenance.common.a_infrastructure.cache.impl;

import com.zte.uedm.component.kafka.producer.constants.KafkaActionOptional;
import com.zte.uedm.maintenance.common.a_infrastructure.cache.manager.AdapterCacheManager;
import com.zte.uedm.maintenance.common.a_infrastructure.cache.strategy.AdapterCacheManagerStrategy;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.HashSet;
import java.util.Set;

public class AdapterCacheManagerStrategyTest {

    @InjectMocks
    private AdapterCacheManagerStrategy adapterCacheManagerStrategy;

    @Mock
    private AdapterCacheManager adapterCacheManager;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /* Started by AICoder, pid:k16aes5fe9u4cd414f7609c400030718e2e34b3c */
    @Test
    public void dealKafka() {
        Set<String> ids = new HashSet<>();
        ids.add("1");
        ids.add("2");
        try {
            Mockito.doReturn(new HashSet<>()).when(adapterCacheManager).dealAdd(Mockito.anySet());
            adapterCacheManagerStrategy.dealKafka(KafkaActionOptional.KAFKA_ACTION_CREATE, ids);
            adapterCacheManagerStrategy.dealKafka(KafkaActionOptional.KAFKA_ACTION_DELETE, ids);
            adapterCacheManagerStrategy.dealKafka(KafkaActionOptional.KAFKA_ACTION_UPDATE, ids);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }
    /* Ended by AICoder, pid:k16aes5fe9u4cd414f7609c400030718e2e34b3c */

}