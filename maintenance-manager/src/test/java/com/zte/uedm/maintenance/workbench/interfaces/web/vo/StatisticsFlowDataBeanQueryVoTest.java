package com.zte.uedm.maintenance.workbench.interfaces.web.vo;

import com.zte.uedm.maintenance.manager.infrastructure.common.util.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

public class StatisticsFlowDataBeanQueryVoTest {

    @Test
    public void test() throws Exception {
        StatisticsFlowDataQueryVo statisticsFlowDataQueryVo = new StatisticsFlowDataQueryVo();
        PojoTestUtil.TestForPojo(statisticsFlowDataQueryVo.getClass());
        statisticsFlowDataQueryVo.setStartTime("xxx");
        Assert.assertNotNull(statisticsFlowDataQueryVo.toString());
        Assert.assertEquals("xxx",  statisticsFlowDataQueryVo.getStartTime());
    }
}
