package com.zte.uedm.maintenance.drill.infrastructure.common.config;

import com.zte.oes.dexcloud.redis.redisson.service.RedissonException;
import com.zte.oes.dexcloud.redis.redisson.service.RedissonService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;
import org.redisson.api.RBucket;
import org.redisson.api.RFuture;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2023/7/6
 **/
public class RedisLockConfigTest1 {

    @Mock
    RedissonService redissonService;

    @InjectMocks
    RedisLockConfig redisLockConfig;

    @Before
    public void setUp(){
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void init111() throws RedissonException, ExecutionException, InterruptedException {
        RedissonClient redissonClient = mock(RedissonClient.class);
        RedissonService redissonService1 = mock(RedissonService.class);
        RBucket rBucket = mock(RBucket.class);
        redisLockConfig = new RedisLockConfig();
        redisLockConfig.setRedissonService(redissonService1);
        when(redissonService1.getRedissonClient()).thenReturn(redissonClient);
        redisLockConfig.init();

        when(redissonClient.getBucket(anyString())).thenReturn(rBucket);
        RFuture rFuture = mock(RFuture.class);
        when(rBucket.trySetAsync(1,11,TimeUnit.MINUTES)).thenReturn(rFuture);
        when(rFuture.get()).thenReturn(true);
        boolean result = redisLockConfig.acquireLock("12312", 11);
        redisLockConfig.existLock("123");
        redisLockConfig.releaseLock("123");

        Assert.assertEquals(true, result);
    }
}