package com.zte.uedm.maintenance.manager.domain.aggregate.maintenancetask.model;

import com.zte.uedm.maintenance.manager.infrastructure.common.util.PojoTestUtil;
import org.junit.Assert;
import org.junit.Test;

public class MaintenanceTaskEntityTest {

    @Test
    public void test() throws Exception {
        MaintenanceTaskEntity task = new MaintenanceTaskEntity();
        PojoTestUtil.TestForPojo(MaintenanceTaskEntity.class);
        Assert.assertEquals(new MaintenanceTaskEntity().toString(),task.toString());
    }
}
