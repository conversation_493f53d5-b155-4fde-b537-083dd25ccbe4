package com.zte.uedm.maintenance.workbench.infrastructure.repository.notice.persistence;

import com.zte.uedm.maintenance.workbench.application.query.StatisticsQuery;
import com.zte.uedm.maintenance.workbench.infrastructure.repository.notice.mapper.FlowStatisticsMapper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;

public class FlowStatisticsRepositoryImplTest {

    @InjectMocks
    private FlowStatisticsRepositoryImpl flowStatisticsRepositoryImpl;

    @Mock
    private FlowStatisticsMapper flowStatisticsMapper;

    @Before
    public void setUp() throws Exception
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void selectStatisticsData() {
        StatisticsQuery statisticsQuery =new StatisticsQuery();
        flowStatisticsRepositoryImpl.selectStatisticsData(statisticsQuery);
        Mockito.doThrow(new RuntimeException()).when(flowStatisticsMapper).selectStatisticsData(Mockito.any());
        flowStatisticsRepositoryImpl.selectStatisticsData(statisticsQuery);
        Assert.assertNotNull("123");
    }

    @Test
    public void selectMaintenanceData() {
        StatisticsQuery statisticsQuery =new StatisticsQuery();
        flowStatisticsRepositoryImpl.selectMaintenanceData(statisticsQuery);
        Mockito.doThrow(new RuntimeException()).when(flowStatisticsMapper).selectMaintenanceData(Mockito.any());
        flowStatisticsRepositoryImpl.selectMaintenanceData(statisticsQuery);
        Assert.assertNotNull("123");
    }

    @Test
    public void selectMalfunctionAndRiskAlreadyData() {
        StatisticsQuery statisticsQuery =new StatisticsQuery();
        flowStatisticsRepositoryImpl.selectMalfunctionAndRiskAlreadyData(statisticsQuery);
        Mockito.doThrow(new RuntimeException()).when(flowStatisticsMapper).selectMalfunctionAndRiskAlreadyData(Mockito.any());
        flowStatisticsRepositoryImpl.selectMalfunctionAndRiskAlreadyData(statisticsQuery);
        Assert.assertNotNull("123");
    }

    @Test
    public void selectPendingTime() {
        StatisticsQuery statisticsQuery =new StatisticsQuery();
        flowStatisticsRepositoryImpl.selectPendingTime(statisticsQuery);
        Mockito.doThrow(new RuntimeException()).when(flowStatisticsMapper).selectPendingTime(Mockito.any());
        flowStatisticsRepositoryImpl.selectPendingTime(statisticsQuery);
        Assert.assertNotNull("123");
    }

    @Test
    public void selectWideNetData() {
        StatisticsQuery statisticsQuery =new StatisticsQuery();
        flowStatisticsRepositoryImpl.selectWideNetData(statisticsQuery);
        Mockito.doThrow(new RuntimeException()).when(flowStatisticsMapper).selectWideNetData(Mockito.any());
        flowStatisticsRepositoryImpl.selectWideNetData(statisticsQuery);
        Assert.assertNotNull("123");
    }
}