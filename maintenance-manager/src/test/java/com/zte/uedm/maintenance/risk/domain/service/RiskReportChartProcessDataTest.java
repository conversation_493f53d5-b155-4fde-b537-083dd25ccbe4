package com.zte.uedm.maintenance.risk.domain.service;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.maintenance.risk.application.query.RiskFormQuery;
import com.zte.uedm.maintenance.risk.domain.aggregate.risk.model.RiskReportChartResponseBean;
import com.zte.uedm.maintenance.risk.infrastructure.common.util.GetMonthOrDataByRange;
import com.zte.uedm.maintenance.risk.interfaces.web.dto.RiskDataDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;


public class RiskReportChartProcessDataTest {

    @InjectMocks
    private RiskReportChartProcessData riskReportChartProcessData;

    @Mock
    private GetMonthOrDataByRange getMonthOrDataByRange;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void getRiskReportChartTypeWrong() {
        RiskFormQuery riskFormQuery = new RiskFormQuery();
        List<RiskDataDTO> riskDataDTOList =  new ArrayList<>();
        RiskDataDTO riskDataDTO = new RiskDataDTO();
        riskFormQuery.setStatisticalType("111");
        riskFormQuery.setStartTime("2022-06-15 15:06:55");
        riskFormQuery.setEndTime("2022-09-15 15:06:55");

        riskDataDTO.setAddFlag(true);
        riskDataDTO.setCreateTime("2022-09-09 15:07:00");
        riskDataDTO.setOperateTime("2022-10-09 15:06:55.723");
        riskDataDTO.setStatus("executeFirst");
        riskDataDTO.setWorkFlowId("7501");
        riskDataDTO.setRiskSource(0);
        riskDataDTO.setRiskLevel(3);
        riskDataDTO.setEmergencyLevel(1);
        riskDataDTO.setEffectLevel(1);
        riskDataDTOList.add(riskDataDTO);
        List<String> rangeList = new ArrayList<>();
        rangeList.add("2022-10-09");
        Mockito.when(getMonthOrDataByRange.getDayBetween(Mockito.any(),Mockito.any())).thenReturn(rangeList);
        List<RiskReportChartResponseBean> list = riskReportChartProcessData.getRiskReportChart(riskFormQuery,riskDataDTOList);
        Assert.assertNull(list);

    }

    @Test
    public void getRiskReportChartTypeDay() {
        RiskFormQuery riskFormQuery = new RiskFormQuery();
        List<RiskDataDTO> riskDataDTOList =  new ArrayList<>();
        RiskDataDTO riskDataDTO = new RiskDataDTO();
        riskFormQuery.setStatisticalType("day");
        riskFormQuery.setStartTime("2022-06-15 15:06:55");
        riskFormQuery.setEndTime("2022-09-15 15:06:55");

        riskDataDTO.setAddFlag(true);
        riskDataDTO.setCreateTime("2022-09-09 15:07:00");
        riskDataDTO.setOperateTime("2022-10-09 15:06:55.723");
        riskDataDTO.setStatus("executeFirst");
        riskDataDTO.setWorkFlowId("7501");
        riskDataDTO.setRiskSource(0);
        riskDataDTO.setRiskLevel(3);
        riskDataDTO.setEmergencyLevel(1);
        riskDataDTO.setEffectLevel(1);
        riskDataDTOList.add(riskDataDTO);
        List<String> rangeList = new ArrayList<>();
        rangeList.add("2022-10-09");
        Mockito.when(getMonthOrDataByRange.getDayBetween(Mockito.any(),Mockito.any())).thenReturn(rangeList);
        List<RiskReportChartResponseBean> list = riskReportChartProcessData.getRiskReportChart(riskFormQuery,riskDataDTOList);
        System.out.println(JSON.toJSONString(list));

        Assert.assertEquals(JSON.toJSONString(list),"[{\"tampNum\":{\"numAdd\":0,\"numSolved\":0,\"numUnsolved\":1},\"timestamp\":\"2022-10-09\"}]");

    }
    @Test
    public void getRiskReportChartTypeNull30() {
        RiskFormQuery riskFormQuery = new RiskFormQuery();
        List<RiskDataDTO> riskDataDTOList =  new ArrayList<>();
        RiskDataDTO riskDataDTO = new RiskDataDTO();
//        riskFormQuery.setStatisticalType("day");
        riskFormQuery.setStartTime("2022-10-15 15:06:55");
        riskFormQuery.setEndTime("2022-09-15 15:06:55");

        riskDataDTO.setAddFlag(true);
        riskDataDTO.setCreateTime("2022-09-09 15:07:00");
        riskDataDTO.setOperateTime("2022-10-09 15:06:55.723");
        riskDataDTO.setStatus("executeFirst");
        riskDataDTO.setWorkFlowId("7501");
        riskDataDTO.setRiskSource(0);
        riskDataDTO.setRiskLevel(3);
        riskDataDTO.setEmergencyLevel(1);
        riskDataDTO.setEffectLevel(1);
        riskDataDTOList.add(riskDataDTO);
        List<String> rangeList = new ArrayList<>();
        rangeList.add("2022-10-09");
        Mockito.when(getMonthOrDataByRange.getDayBetween(Mockito.any(),Mockito.any())).thenReturn(rangeList);
        List<RiskReportChartResponseBean> list = riskReportChartProcessData.getRiskReportChart(riskFormQuery,riskDataDTOList);
        System.out.println(JSON.toJSONString(list));

        Assert.assertEquals(JSON.toJSONString(list),"[{\"tampNum\":{\"numAdd\":0,\"numSolved\":0,\"numUnsolved\":1},\"timestamp\":\"2022-10-09\"}]");

    }

    @Test
    public void getRiskReportChartTypeNull100() {
        RiskFormQuery riskFormQuery = new RiskFormQuery();
        List<RiskDataDTO> riskDataDTOList =  new ArrayList<>();
        RiskDataDTO riskDataDTO = new RiskDataDTO();
//        riskFormQuery.setStatisticalType("day");
        riskFormQuery.setStartTime("2022-06-15 15:06:55");
        riskFormQuery.setEndTime("2022-09-15 15:06:55");

        riskDataDTO.setAddFlag(true);
        riskDataDTO.setCreateTime("2022-09-09 15:07:00");
        riskDataDTO.setOperateTime("2022-10-09 15:06:55.723");
        riskDataDTO.setStatus("executeFirst");
        riskDataDTO.setWorkFlowId("7501");
        riskDataDTO.setRiskSource(0);
        riskDataDTO.setRiskLevel(3);
        riskDataDTO.setEmergencyLevel(1);
        riskDataDTO.setEffectLevel(1);

        RiskDataDTO riskDataDTO1 = new RiskDataDTO();
        riskDataDTO1.setAddFlag(true);
        riskDataDTO1.setCreateTime("2022-09-09 15:07:00");
        riskDataDTO1.setOperateTime("2022-09-09 15:06:55.723");
        riskDataDTO1.setStatus("cacel");
        riskDataDTO1.setWorkFlowId("7501");
        riskDataDTO1.setRiskSource(0);
        riskDataDTO1.setRiskLevel(3);
        riskDataDTO1.setEmergencyLevel(1);
        riskDataDTO1.setEffectLevel(1);

        RiskDataDTO riskDataDTO2 = new RiskDataDTO();
        riskDataDTO2.setAddFlag(true);
        riskDataDTO2.setCreateTime("2022-09-09 15:07:00");
        riskDataDTO2.setOperateTime("2022-09-09 15:06:55.723");
        riskDataDTO2.setStatus("over");
        riskDataDTO2.setWorkFlowId("7501");
        riskDataDTO2.setRiskSource(0);
        riskDataDTO2.setRiskLevel(3);
        riskDataDTO2.setEmergencyLevel(1);
        riskDataDTO2.setEffectLevel(1);

        RiskDataDTO riskDataDTO3 = new RiskDataDTO();
        riskDataDTO3.setAddFlag(true);
        riskDataDTO3.setCreateTime("2022-01-09 15:07:00");
        riskDataDTO3.setOperateTime("2022-12-09 15:06:55.723");
        riskDataDTO3.setStatus("over");
        riskDataDTO3.setWorkFlowId("7501");
        riskDataDTO3.setRiskSource(0);
        riskDataDTO3.setRiskLevel(3);
        riskDataDTO3.setEmergencyLevel(1);
        riskDataDTO3.setEffectLevel(1);


        riskDataDTOList.add(riskDataDTO);
        riskDataDTOList.add(riskDataDTO1);
        riskDataDTOList.add(riskDataDTO2);
        riskDataDTOList.add(riskDataDTO3);
        List<String> rangeList = new ArrayList<>();
        rangeList.add("2022-09");
        List<String> rangeListDay = new ArrayList<>();
        rangeListDay.add("2022-01-01");rangeListDay.add("2022-02-01");rangeListDay.add("2022-03-01");rangeListDay.add("2022-04-01");rangeListDay.add("2022-05-01");
        rangeListDay.add("2022-01-02");rangeListDay.add("2022-02-02");rangeListDay.add("2022-03-02");rangeListDay.add("2022-04-02");rangeListDay.add("2022-05-02");
        rangeListDay.add("2022-01-03");rangeListDay.add("2022-02-03");rangeListDay.add("2022-03-03");rangeListDay.add("2022-04-03");rangeListDay.add("2022-05-03");
        rangeListDay.add("2022-01-04");rangeListDay.add("2022-02-04");rangeListDay.add("2022-03-04");rangeListDay.add("2022-04-04");rangeListDay.add("2022-05-04");
        rangeListDay.add("2022-01-05");rangeListDay.add("2022-02-05");rangeListDay.add("2022-03-05");rangeListDay.add("2022-04-05");rangeListDay.add("2022-05-05");
        rangeListDay.add("2022-01-06");rangeListDay.add("2022-02-06");rangeListDay.add("2022-03-06");rangeListDay.add("2022-04-06");rangeListDay.add("2022-05-06");
        rangeListDay.add("2022-01-07");rangeListDay.add("2022-02-07");rangeListDay.add("2022-03-07");rangeListDay.add("2022-04-07");rangeListDay.add("2022-05-07");
        rangeListDay.add("2022-01-08");rangeListDay.add("2022-02-08");rangeListDay.add("2022-03-08");rangeListDay.add("2022-04-08");rangeListDay.add("2022-05-08");
        rangeListDay.add("2022-01-09");rangeListDay.add("2022-02-09");rangeListDay.add("2022-03-09");rangeListDay.add("2022-04-09");rangeListDay.add("2022-05-09");
        rangeListDay.add("2022-06-10");rangeListDay.add("2022-07-10");rangeListDay.add("2022-08-10");rangeListDay.add("2022-09-10");rangeListDay.add("2022-10-10");
        rangeListDay.add("2022-06-01");rangeListDay.add("2022-07-01");rangeListDay.add("2022-08-01");rangeListDay.add("2022-09-01");rangeListDay.add("2022-10-01");
        rangeListDay.add("2022-06-02");rangeListDay.add("2022-07-02");rangeListDay.add("2022-08-02");rangeListDay.add("2022-09-02");rangeListDay.add("2022-10-02");
        rangeListDay.add("2022-06-03");rangeListDay.add("2022-07-03");rangeListDay.add("2022-08-03");rangeListDay.add("2022-09-03");rangeListDay.add("2022-10-03");
        rangeListDay.add("2022-06-04");rangeListDay.add("2022-07-04");rangeListDay.add("2022-08-04");rangeListDay.add("2022-09-04");rangeListDay.add("2022-10-04");
        rangeListDay.add("2022-06-05");rangeListDay.add("2022-07-05");rangeListDay.add("2022-08-05");rangeListDay.add("2022-09-05");rangeListDay.add("2022-10-05");
        rangeListDay.add("2022-06-06");rangeListDay.add("2022-07-06");rangeListDay.add("2022-08-06");rangeListDay.add("2022-09-06");rangeListDay.add("2022-10-06");
        rangeListDay.add("2022-06-07");rangeListDay.add("2022-07-07");rangeListDay.add("2022-08-07");rangeListDay.add("2022-09-07");rangeListDay.add("2022-10-07");
        rangeListDay.add("2022-06-08");rangeListDay.add("2022-07-08");rangeListDay.add("2022-08-08");rangeListDay.add("2022-09-08");rangeListDay.add("2022-10-08");
        rangeListDay.add("2022-06-09");rangeListDay.add("2022-07-09");rangeListDay.add("2022-08-09");rangeListDay.add("2022-09-09");rangeListDay.add("2022-10-09");
        rangeListDay.add("2022-06-10");rangeListDay.add("2022-07-10");rangeListDay.add("2022-08-10");rangeListDay.add("2022-09-10");rangeListDay.add("2022-10-10");
        Mockito.when(getMonthOrDataByRange.getMonthBetween(Mockito.any(),Mockito.any())).thenReturn(rangeList);
        Mockito.when(getMonthOrDataByRange.getDayBetween(Mockito.any(),Mockito.any())).thenReturn(rangeListDay);
        List<RiskReportChartResponseBean> list = riskReportChartProcessData.getRiskReportChart(riskFormQuery,riskDataDTOList);
        Assert.assertEquals(JSON.toJSONString(list),"[{\"tampNum\":{\"numAdd\":3,\"numSolved\":2,\"numUnsolved\":2},\"timestamp\":\"2022-09\"}]");

    }

    @Test
    public void getRiskReportChartTypeMonth() {
        RiskFormQuery riskFormQuery = new RiskFormQuery();
        List<RiskDataDTO> riskDataDTOList =  new ArrayList<>();
        RiskDataDTO riskDataDTO = new RiskDataDTO();
        riskFormQuery.setStatisticalType("month");
        riskFormQuery.setStartTime("2022-06-15 15:06:55");
        riskFormQuery.setEndTime("2022-10-15 15:06:55");

        riskDataDTO.setAddFlag(true);
        riskDataDTO.setCreateTime("2022-09-09 15:07:00");
        riskDataDTO.setOperateTime("2022-10-09 15:06:55.723");
        riskDataDTO.setStatus("executeFirst");
        riskDataDTO.setWorkFlowId("7501");
        riskDataDTO.setRiskSource(0);
        riskDataDTO.setRiskLevel(3);
        riskDataDTO.setEmergencyLevel(1);
        riskDataDTO.setEffectLevel(1);
        riskDataDTOList.add(riskDataDTO);
        List<String> rangeList = new ArrayList<>();
        rangeList.add("2022-09");
        Mockito.when(getMonthOrDataByRange.getMonthBetween(Mockito.any(),Mockito.any())).thenReturn(rangeList);
        List<RiskReportChartResponseBean> list = riskReportChartProcessData.getRiskReportChart(riskFormQuery,riskDataDTOList);
        System.out.println(JSON.toJSONString(list));

        Assert.assertEquals(JSON.toJSONString(list),"[{\"tampNum\":{\"numAdd\":0,\"numSolved\":0,\"numUnsolved\":0},\"timestamp\":\"2022-09\"}]");

    }

//    @Test
//    public void statistics() {
//    }
}