package com.zte.uedm.maintenance.risk.infrastructure.repository.risk.mapper;

import com.zte.uedm.maintenance.risk.application.query.RiskFormQuery;
import com.zte.uedm.maintenance.risk.infrastructure.repository.risk.po.RiskDataPO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

/**
 * 风险单时间查询逻辑测试
 * 用于验证修复后的时间查询逻辑是否正确
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RiskMapperTimeQueryTest {

    @Resource
    private RiskMapper riskMapper;

    /**
     * 测试单天查询逻辑
     * 验证2025-06-11当天的数据能够正确查询
     */
    @Test
    public void testSingleDayQuery() {
        RiskFormQuery query = createTestQuery();
        
        // 设置查询2025-06-11当天
        query.setStartTime("2025-06-11 00:00:00");
        query.setEndTime("2025-06-11 23:59:59");
        query.setStartTimestamp(Timestamp.valueOf("2025-06-11 00:00:00"));
        query.setEndTimestamp(Timestamp.valueOf("2025-06-11 23:59:59"));
        
        List<RiskDataPO> result = riskMapper.selectRiskData(query);
        
        // 验证结果不为空（假设有测试数据）
        Assert.assertNotNull("Single day query result should not be null", result);
        
        // 打印结果用于调试
        System.out.println("Single day query result count: " + result.size());
        for (RiskDataPO data : result) {
            System.out.println("WorkflowId: " + data.getWorkFlowId() + 
                             ", Status: " + data.getStatus() + 
                             ", CreateTime: " + data.getCreateTime() + 
                             ", AddFlag: " + data.getAddFlag());
        }
    }

    /**
     * 测试跨天查询逻辑
     * 验证2025-06-11到2025-06-12的数据能够正确查询
     */
    @Test
    public void testMultiDayQuery() {
        RiskFormQuery query = createTestQuery();
        
        // 设置查询2025-06-11到2025-06-12
        query.setStartTime("2025-06-11 00:00:00");
        query.setEndTime("2025-06-12 23:59:59");
        query.setStartTimestamp(Timestamp.valueOf("2025-06-11 00:00:00"));
        query.setEndTimestamp(Timestamp.valueOf("2025-06-12 23:59:59"));
        
        List<RiskDataPO> result = riskMapper.selectRiskData(query);
        
        // 验证结果不为空
        Assert.assertNotNull("Multi day query result should not be null", result);
        
        // 打印结果用于调试
        System.out.println("Multi day query result count: " + result.size());
        for (RiskDataPO data : result) {
            System.out.println("WorkflowId: " + data.getWorkFlowId() + 
                             ", Status: " + data.getStatus() + 
                             ", CreateTime: " + data.getCreateTime() + 
                             ", AddFlag: " + data.getAddFlag());
        }
    }

    /**
     * 测试第二天单独查询
     * 验证2025-06-12当天的数据能够正确查询
     */
    @Test
    public void testSecondDayQuery() {
        RiskFormQuery query = createTestQuery();
        
        // 设置查询2025-06-12当天
        query.setStartTime("2025-06-12 00:00:00");
        query.setEndTime("2025-06-12 23:59:59");
        query.setStartTimestamp(Timestamp.valueOf("2025-06-12 00:00:00"));
        query.setEndTimestamp(Timestamp.valueOf("2025-06-12 23:59:59"));
        
        List<RiskDataPO> result = riskMapper.selectRiskData(query);
        
        // 验证结果不为空
        Assert.assertNotNull("Second day query result should not be null", result);
        
        // 打印结果用于调试
        System.out.println("Second day query result count: " + result.size());
        for (RiskDataPO data : result) {
            System.out.println("WorkflowId: " + data.getWorkFlowId() + 
                             ", Status: " + data.getStatus() + 
                             ", CreateTime: " + data.getCreateTime() + 
                             ", AddFlag: " + data.getAddFlag());
        }
    }

    /**
     * 创建测试查询对象
     */
    private RiskFormQuery createTestQuery() {
        RiskFormQuery query = new RiskFormQuery();
        
        // 设置基本查询条件（包含所有可能的值）
        query.setRiskSource(Arrays.asList(1, 2, 3, 4));
        query.setEffectLevel(Arrays.asList(1, 2, 3, 4));
        query.setEmergencyLevel(Arrays.asList(1, 2, 3, 4));
        query.setRiskLevel(Arrays.asList(1, 2, 3, 4));
        query.setStatisticalType("day");
        
        return query;
    }

    /**
     * 测试addFlag字段的正确性
     * 验证addFlag字段能够正确标识新增的风险单
     */
    @Test
    public void testAddFlagLogic() {
        RiskFormQuery query = createTestQuery();
        
        // 设置查询2025-06-11当天
        query.setStartTime("2025-06-11 00:00:00");
        query.setEndTime("2025-06-11 23:59:59");
        query.setStartTimestamp(Timestamp.valueOf("2025-06-11 00:00:00"));
        query.setEndTimestamp(Timestamp.valueOf("2025-06-11 23:59:59"));
        
        List<RiskDataPO> result = riskMapper.selectRiskData(query);
        
        // 验证addFlag逻辑
        for (RiskDataPO data : result) {
            String createTime = data.getCreateTime();
            Boolean addFlag = data.getAddFlag();
            
            if (createTime != null) {
                Timestamp createTimestamp = Timestamp.valueOf(createTime);
                Timestamp startTimestamp = query.getStartTimestamp();
                Timestamp endTimestamp = query.getEndTimestamp();
                
                // 验证addFlag的逻辑正确性
                boolean expectedAddFlag = createTimestamp.compareTo(startTimestamp) >= 0 && 
                                        createTimestamp.compareTo(endTimestamp) <= 0;
                
                Assert.assertEquals("AddFlag should match create time range check for workflow: " + data.getWorkFlowId(), 
                                  expectedAddFlag, addFlag);
            }
        }
    }
}
