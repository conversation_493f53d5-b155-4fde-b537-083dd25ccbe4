package com.zte.uedm.maintenance.duty.interfaces.web;

import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.maintenance.duty.domain.service.WorkTableService;
import com.zte.uedm.maintenance.duty.interfaces.web.dto.DutyOverviewCalendarDto;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;


public class WorkTableControllerTest {
    @InjectMocks
    private WorkTableController workTableController;

    @Mock
    private WorkTableService workTableService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getAllUserName() throws UedmException {
        List<String> userNameRes = new ArrayList<>();
        Mockito.when(workTableService.getAllUserName("en-US")).thenReturn(userNameRes);
        workTableController.getAllUserName("en-US");
        Mockito.doThrow(UedmException.class).when(workTableService).getAllUserName("en-US");
        workTableController.getAllUserName("en-US");
        Mockito.verify(workTableService, Mockito.times(2)).getAllUserName("en-US");
    }

    @Test
    public void getDutyCondition() throws UedmException {
        List<DutyOverviewCalendarDto> res = new ArrayList<>();
        Mockito.when(workTableService.getOverviewCalendarAll("admin")).thenReturn(res);
        workTableController.getDutyCondition("admin");
        Mockito.doThrow(UedmException.class).when(workTableService).getOverviewCalendarAll("mm");
        workTableController.getDutyCondition("mm");
        Mockito.verify(workTableService, Mockito.times(2)).getOverviewCalendarAll(Mockito.anyString());
    }
}