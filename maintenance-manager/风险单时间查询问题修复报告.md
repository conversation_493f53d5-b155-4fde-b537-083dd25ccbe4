# 风险单时间查询问题修复报告

## 问题概述

### 问题描述
风险单报表查询接口 `/risk/get_risk_form` 存在时间查询逻辑问题：
1. 查询2025-06-11当天数据返回0条记录
2. 查询2025-06-12当天数据能正常返回
3. 查询2025-06-11到2025-06-12跨天数据却能查询出11号的数据

### 影响范围
- 风险单报表查询功能
- 风险单数据统计准确性
- 用户对系统数据一致性的信任

## 根因分析

### 1. 时间字段混用问题

**问题代码位置**: `RiskMapper.xml` 第338-365行

**核心问题**:
- SQL查询中混合使用了两个不同含义的时间字段：
  - `act_hi_taskinst.start_time_`: 工作流任务开始时间
  - `workflow_instance_risk.flexible_str5`: 风险单创建时间（malfunctionHappenTime）

### 2. SQL查询逻辑缺陷

**原始问题逻辑**:
```sql
WHERE (a.start_time_ BETWEEN #{startTimestamp} AND #{endTimestamp}) 
   OR (a.start_time_ < #{startTimestamp} AND a.task_def_key_ NOT IN ('cacel','over'))
```

**问题分析**:
1. 当查询单天时，某些工作流状态的记录可能因为 `start_time_` 不在范围内而被排除
2. 当查询跨天时，由于OR条件的存在，这些记录又能被包含进来
3. 导致单天查询和跨天查询结果不一致

### 3. addFlag计算逻辑不一致

**原始代码**:
```sql
case when c.flexible_str5 between #{startTimestamp} and #{endTimestamp} then true else false end as addFlag
```

**问题**: addFlag使用风险单创建时间判断，但主查询条件使用工作流时间，导致逻辑不一致。

## 修复方案

### 1. 统一时间查询逻辑

**修复策略**: 以风险单创建时间为主要查询条件，工作流时间为辅助条件

**修复后的SQL逻辑**:
```sql
WHERE (
    -- 风险单创建时间在查询范围内的记录
    c.flexible_str5 BETWEEN #{startTimestamp} AND #{endTimestamp}
    OR 
    -- 风险单创建时间在查询范围外，但当前状态不是终结状态的记录
    (c.flexible_str5 < #{startTimestamp} AND b.task_def_key_ NOT IN ('cacel','over'))
)
```

### 2. 修复工作流查询条件

**原始问题**:
```sql
AND start_time_ < #{endTimestamp}
```

**修复为**:
```sql
AND start_time_ <= #{endTimestamp}
```

**原因**: 使用 `<=` 确保边界时间的记录不被遗漏

### 3. 增强查询覆盖范围

**新增条件**:
```sql
OR (a.start_time_ > #{endTimestamp} AND a.task_def_key_ NOT IN ('cacel','over'))
```

**目的**: 确保所有未完结的风险单都能被正确查询到

## 修复前后对比

### 修复前
```sql
-- 主要问题：时间条件混乱，逻辑不一致
WHERE (a.start_time_ BETWEEN #{startTimestamp} AND #{endTimestamp}) 
   OR (a.start_time_ < #{startTimestamp} AND a.task_def_key_ NOT IN ('cacel','over'))
```

### 修复后
```sql
-- 统一以风险单创建时间为主要条件
WHERE (
    c.flexible_str5 BETWEEN #{startTimestamp} AND #{endTimestamp}
    OR 
    (c.flexible_str5 < #{startTimestamp} AND b.task_def_key_ NOT IN ('cacel','over'))
)
```

## 测试验证

### 1. 单元测试
创建了 `RiskMapperTimeQueryTest` 测试类，包含以下测试用例：
- `testSingleDayQuery()`: 测试单天查询逻辑
- `testMultiDayQuery()`: 测试跨天查询逻辑  
- `testSecondDayQuery()`: 测试第二天单独查询
- `testAddFlagLogic()`: 测试addFlag字段正确性

### 2. 集成测试建议
1. 准备测试数据：创建不同时间的风险单记录
2. 执行查询测试：验证各种时间范围的查询结果
3. 对比修复前后：确保修复后结果的一致性和正确性

## 潜在影响评估

### 正面影响
1. **数据一致性**: 解决了单天查询和跨天查询结果不一致的问题
2. **查询准确性**: 确保所有符合条件的风险单都能被正确查询
3. **用户体验**: 提升用户对系统数据准确性的信任

### 风险评估
1. **兼容性风险**: 低 - 修复主要是逻辑优化，不涉及接口变更
2. **性能影响**: 低 - SQL优化可能略微提升查询性能
3. **数据风险**: 低 - 修复不会影响现有数据，只是查询逻辑优化

### 回滚方案
如果修复后出现问题，可以通过以下方式回滚：
1. 恢复原始的 `RiskMapper.xml` 文件
2. 重新部署应用
3. 验证功能正常

## 部署建议

### 1. 部署前准备
- 备份当前的 `RiskMapper.xml` 文件
- 准备测试数据和测试用例
- 通知相关用户可能的短暂服务中断

### 2. 部署步骤
1. 在测试环境验证修复效果
2. 执行单元测试和集成测试
3. 部署到生产环境
4. 验证生产环境功能正常

### 3. 部署后验证
- 执行关键查询场景测试
- 监控系统日志和性能指标
- 收集用户反馈

## 总结

本次修复解决了风险单时间查询逻辑的根本问题，通过统一时间查询条件和优化SQL逻辑，确保了查询结果的一致性和准确性。修复方案风险较低，建议尽快部署到生产环境。
